<!-- Reusable Modal Component -->
<div 
  x-show="show" 
  x-transition:enter="transition ease-out duration-300"
  x-transition:enter-start="opacity-0"
  x-transition:enter-end="opacity-100"
  x-transition:leave="transition ease-in duration-200"
  x-transition:leave-start="opacity-100"
  x-transition:leave-end="opacity-0"
  class="fixed inset-0 z-50 overflow-y-auto"
  style="display: none;"
>
  <!-- Backdrop -->
  <div 
    class="fixed inset-0 bg-black bg-opacity-50 backdrop-blur-sm"
    @click="show = false"
  ></div>

  <!-- Modal Container -->
  <div class="flex min-h-full items-center justify-center p-4">
    <!-- Modal Content -->
    <div 
      x-show="show"
      x-transition:enter="transition ease-out duration-300"
      x-transition:enter-start="opacity-0 transform scale-95"
      x-transition:enter-end="opacity-100 transform scale-100"
      x-transition:leave="transition ease-in duration-200"
      x-transition:leave-start="opacity-100 transform scale-100"
      x-transition:leave-end="opacity-0 transform scale-95"
      class="relative bg-white rounded-lg shadow-xl max-w-lg w-full mx-4"
      @click.stop
    >
      <!-- Modal Header -->
      <div class="flex items-center justify-between p-6 border-b border-gray-200">
        <h3 class="text-lg font-semibold text-gray-900" x-text="title"></h3>
        <button 
          @click="show = false"
          class="text-gray-400 hover:text-gray-600 transition-colors"
        >
          <svg class="h-6 w-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
          </svg>
        </button>
      </div>

      <!-- Modal Body -->
      <div class="p-6">
        <div x-html="content"></div>
      </div>

      <!-- Modal Footer -->
      <div class="flex items-center justify-end space-x-3 p-6 border-t border-gray-200">
        <button 
          @click="show = false"
          class="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors"
        >
          Cancelar
        </button>
        <button 
          @click="onConfirm && onConfirm()"
          class="px-4 py-2 text-sm font-medium text-white bg-primary-600 border border-transparent rounded-lg hover:bg-primary-700 transition-colors"
          x-text="confirmText || 'Confirmar'"
        ></button>
      </div>
    </div>
  </div>
</div>

<!-- Confirmation Modal Variant -->
<template x-if="type === 'confirm'">
  <div 
    x-show="show" 
    x-transition:enter="transition ease-out duration-300"
    x-transition:enter-start="opacity-0"
    x-transition:enter-end="opacity-100"
    x-transition:leave="transition ease-in duration-200"
    x-transition:leave-start="opacity-100"
    x-transition:leave-end="opacity-0"
    class="fixed inset-0 z-50 overflow-y-auto"
    style="display: none;"
  >
    <!-- Backdrop -->
    <div 
      class="fixed inset-0 bg-black bg-opacity-50 backdrop-blur-sm"
      @click="show = false"
    ></div>

    <!-- Modal Container -->
    <div class="flex min-h-full items-center justify-center p-4">
      <!-- Modal Content -->
      <div 
        x-show="show"
        x-transition:enter="transition ease-out duration-300"
        x-transition:enter-start="opacity-0 transform scale-95"
        x-transition:enter-end="opacity-100 transform scale-100"
        x-transition:leave="transition ease-in duration-200"
        x-transition:leave-start="opacity-100 transform scale-100"
        x-transition:leave-end="opacity-0 transform scale-95"
        class="relative bg-white rounded-lg shadow-xl max-w-md w-full mx-4"
        @click.stop
      >
        <!-- Modal Content -->
        <div class="p-6">
          <!-- Icon -->
          <div class="flex items-center justify-center w-12 h-12 mx-auto mb-4 bg-red-100 rounded-full">
            <svg class="w-6 h-6 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
            </svg>
          </div>

          <!-- Title -->
          <h3 class="text-lg font-semibold text-gray-900 text-center mb-2" x-text="title"></h3>
          
          <!-- Message -->
          <p class="text-sm text-gray-600 text-center mb-6" x-text="message"></p>

          <!-- Actions -->
          <div class="flex items-center justify-center space-x-3">
            <button 
              @click="show = false"
              class="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors"
            >
              Cancelar
            </button>
            <button 
              @click="onConfirm && onConfirm(); show = false"
              class="px-4 py-2 text-sm font-medium text-white bg-red-600 border border-transparent rounded-lg hover:bg-red-700 transition-colors"
              x-text="confirmText || 'Confirmar'"
            ></button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<!-- Success Modal Variant -->
<template x-if="type === 'success'">
  <div 
    x-show="show" 
    x-transition:enter="transition ease-out duration-300"
    x-transition:enter-start="opacity-0"
    x-transition:enter-end="opacity-100"
    x-transition:leave="transition ease-in duration-200"
    x-transition:leave-start="opacity-100"
    x-transition:leave-end="opacity-0"
    class="fixed inset-0 z-50 overflow-y-auto"
    style="display: none;"
  >
    <!-- Backdrop -->
    <div 
      class="fixed inset-0 bg-black bg-opacity-50 backdrop-blur-sm"
      @click="show = false"
    ></div>

    <!-- Modal Container -->
    <div class="flex min-h-full items-center justify-center p-4">
      <!-- Modal Content -->
      <div 
        x-show="show"
        x-transition:enter="transition ease-out duration-300"
        x-transition:enter-start="opacity-0 transform scale-95"
        x-transition:enter-end="opacity-100 transform scale-100"
        x-transition:leave="transition ease-in duration-200"
        x-transition:leave-start="opacity-100 transform scale-100"
        x-transition:leave-end="opacity-0 transform scale-95"
        class="relative bg-white rounded-lg shadow-xl max-w-md w-full mx-4"
        @click.stop
      >
        <!-- Modal Content -->
        <div class="p-6">
          <!-- Icon -->
          <div class="flex items-center justify-center w-12 h-12 mx-auto mb-4 bg-green-100 rounded-full">
            <svg class="w-6 h-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
            </svg>
          </div>

          <!-- Title -->
          <h3 class="text-lg font-semibold text-gray-900 text-center mb-2" x-text="title"></h3>
          
          <!-- Message -->
          <p class="text-sm text-gray-600 text-center mb-6" x-text="message"></p>

          <!-- Actions -->
          <div class="flex items-center justify-center">
            <button 
              @click="show = false; onConfirm && onConfirm()"
              class="px-6 py-2 text-sm font-medium text-white bg-green-600 border border-transparent rounded-lg hover:bg-green-700 transition-colors"
            >
              OK
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
  // Modal utility functions
  window.showModal = function(options) {
    return {
      show: true,
      type: options.type || 'default',
      title: options.title || '',
      message: options.message || '',
      content: options.content || '',
      confirmText: options.confirmText || 'Confirmar',
      onConfirm: options.onConfirm || null
    };
  };

  window.showConfirmModal = function(title, message, onConfirm, confirmText = 'Confirmar') {
    return showModal({
      type: 'confirm',
      title,
      message,
      confirmText,
      onConfirm
    });
  };

  window.showSuccessModal = function(title, message, onConfirm = null) {
    return showModal({
      type: 'success',
      title,
      message,
      onConfirm
    });
  };
</script>
