<!-- <PERSON>nd<PERSON><PERSON> Page -->
<div class="min-h-screen bg-gray-50" x-data="calendarioData()">
  <!-- Header -->
  <%- include('../components/dashboard-header') %>
  
  <!-- Main Content -->
  <div class="flex">
    <!-- Sidebar -->
    <%- include('../components/sidebar') %>
    
    <!-- Main Content Area -->
    <main class="flex-1 p-6">
      <!-- Page Header -->
      <div class="mb-8">
        <div class="flex items-center justify-between">
          <div>
            <h1 class="text-2xl font-bold text-gray-900 mb-2">Calendário de Reservas</h1>
            <p class="text-gray-600">Visualize todas as reservas em um calendário</p>
          </div>
          <button 
            @click="window.location.href = '/nova-reserva'"
            class="bg-primary-600 hover:bg-primary-700 text-white px-4 py-2 rounded-lg flex items-center space-x-2 transition-colors"
          >
            <svg class="h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
            </svg>
            <span>Nova Reserva</span>
          </button>
        </div>
      </div>

      <!-- Calendar Controls -->
      <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-4 mb-6">
        <div class="flex items-center justify-between">
          <!-- Navigation -->
          <div class="flex items-center space-x-4">
            <button 
              @click="previousMonth()"
              class="p-2 text-gray-400 hover:text-gray-600 rounded-lg hover:bg-gray-100"
            >
              <svg class="h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path>
              </svg>
            </button>
            
            <h2 class="text-lg font-semibold text-gray-900" x-text="currentMonthYear"></h2>
            
            <button 
              @click="nextMonth()"
              class="p-2 text-gray-400 hover:text-gray-600 rounded-lg hover:bg-gray-100"
            >
              <svg class="h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
              </svg>
            </button>

            <button 
              @click="goToToday()"
              class="px-3 py-1 text-sm bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200 transition-colors"
            >
              Hoje
            </button>
          </div>

          <!-- View Toggle -->
          <div class="flex items-center space-x-2">
            <span class="text-sm text-gray-500">Visualização:</span>
            <select 
              x-model="viewMode"
              class="text-sm border border-gray-300 rounded-lg px-3 py-1 focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
            >
              <option value="month">Mês</option>
              <option value="week">Semana</option>
              <option value="day">Dia</option>
            </select>
          </div>
        </div>
      </div>

      <!-- Calendar Grid -->
      <div class="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
        <!-- Calendar Header -->
        <div class="grid grid-cols-7 bg-gray-50 border-b border-gray-200">
          <template x-for="day in weekDays" :key="day">
            <div class="p-4 text-center text-sm font-medium text-gray-700" x-text="day"></div>
          </template>
        </div>

        <!-- Calendar Body -->
        <div class="grid grid-cols-7">
          <template x-for="day in calendarDays" :key="day.date">
            <div 
              class="min-h-32 p-2 border-r border-b border-gray-100 relative"
              :class="{
                'bg-gray-50': !day.isCurrentMonth,
                'bg-blue-50': day.isToday,
                'hover:bg-gray-50': day.isCurrentMonth && !day.isToday
              }"
            >
              <!-- Day Number -->
              <div 
                class="text-sm font-medium mb-1"
                :class="{
                  'text-gray-400': !day.isCurrentMonth,
                  'text-blue-600': day.isToday,
                  'text-gray-900': day.isCurrentMonth && !day.isToday
                }"
                x-text="day.dayNumber"
              ></div>

              <!-- Reservations -->
              <div class="space-y-1">
                <template x-for="reserva in getReservasForDay(day.date)" :key="reserva.id">
                  <div 
                    @click="viewReservation(reserva)"
                    class="text-xs p-1 rounded cursor-pointer truncate"
                    :class="{
                      'bg-green-100 text-green-800': reserva.status === 'confirmado',
                      'bg-yellow-100 text-yellow-800': reserva.status === 'pendente',
                      'bg-red-100 text-red-800': reserva.status === 'cancelado'
                    }"
                    :title="reserva.titulo + ' - ' + reserva.sala_nome"
                  >
                    <span x-text="reserva.hora_inicio"></span> <span x-text="reserva.titulo"></span>
                  </div>
                </template>
              </div>

              <!-- Add Event Button -->
              <button 
                x-show="day.isCurrentMonth"
                @click="createReservation(day.date)"
                class="absolute bottom-1 right-1 w-6 h-6 bg-primary-600 text-white rounded-full flex items-center justify-center opacity-0 hover:opacity-100 transition-opacity"
              >
                <svg class="h-3 w-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                </svg>
              </button>
            </div>
          </template>
        </div>
      </div>

      <!-- Legend -->
      <div class="mt-6 bg-white rounded-lg shadow-sm border border-gray-200 p-4">
        <h3 class="text-sm font-medium text-gray-900 mb-3">Legenda</h3>
        <div class="flex flex-wrap gap-4">
          <div class="flex items-center space-x-2">
            <div class="w-3 h-3 bg-green-100 border border-green-200 rounded"></div>
            <span class="text-sm text-gray-600">Confirmado</span>
          </div>
          <div class="flex items-center space-x-2">
            <div class="w-3 h-3 bg-yellow-100 border border-yellow-200 rounded"></div>
            <span class="text-sm text-gray-600">Pendente</span>
          </div>
          <div class="flex items-center space-x-2">
            <div class="w-3 h-3 bg-red-100 border border-red-200 rounded"></div>
            <span class="text-sm text-gray-600">Cancelado</span>
          </div>
        </div>
      </div>
    </main>
  </div>
</div>

<script>
  function calendarioData() {
    return {
      currentDate: new Date(),
      viewMode: 'month',
      reservas: [],
      weekDays: ['Dom', 'Seg', 'Ter', 'Qua', 'Qui', 'Sex', 'Sáb'],

      async init() {
        await this.loadReservas();
      },

      async loadReservas() {
        try {
          const response = await fetch('/api/reservas');
          if (response.ok) {
            this.reservas = await response.json();
          }
        } catch (error) {
          console.error('Erro ao carregar reservas:', error);
        }
      },

      get currentMonthYear() {
        return this.currentDate.toLocaleDateString('pt-BR', { 
          month: 'long', 
          year: 'numeric' 
        });
      },

      get calendarDays() {
        const year = this.currentDate.getFullYear();
        const month = this.currentDate.getMonth();
        
        // Primeiro dia do mês
        const firstDay = new Date(year, month, 1);
        // Último dia do mês
        const lastDay = new Date(year, month + 1, 0);
        
        // Primeiro dia da semana (domingo = 0)
        const startDate = new Date(firstDay);
        startDate.setDate(startDate.getDate() - firstDay.getDay());
        
        // Último dia da semana
        const endDate = new Date(lastDay);
        endDate.setDate(endDate.getDate() + (6 - lastDay.getDay()));
        
        const days = [];
        const currentDate = new Date(startDate);
        
        while (currentDate <= endDate) {
          const today = new Date();
          days.push({
            date: new Date(currentDate),
            dayNumber: currentDate.getDate(),
            isCurrentMonth: currentDate.getMonth() === month,
            isToday: currentDate.toDateString() === today.toDateString()
          });
          currentDate.setDate(currentDate.getDate() + 1);
        }
        
        return days;
      },

      getReservasForDay(date) {
        return this.reservas.filter(reserva => {
          const reservaDate = new Date(reserva.tempo_inicio);
          return reservaDate.toDateString() === date.toDateString();
        }).map(reserva => ({
          ...reserva,
          hora_inicio: new Date(reserva.tempo_inicio).toLocaleTimeString('pt-BR', { 
            hour: '2-digit', 
            minute: '2-digit' 
          })
        }));
      },

      previousMonth() {
        this.currentDate = new Date(this.currentDate.getFullYear(), this.currentDate.getMonth() - 1, 1);
      },

      nextMonth() {
        this.currentDate = new Date(this.currentDate.getFullYear(), this.currentDate.getMonth() + 1, 1);
      },

      goToToday() {
        this.currentDate = new Date();
      },

      createReservation(date) {
        const dateStr = date.toISOString().split('T')[0];
        window.location.href = `/nova-reserva?data=${dateStr}`;
      },

      viewReservation(reserva) {
        // TODO: Implementar modal de detalhes da reserva
        alert(`Reserva: ${reserva.titulo}\nSala: ${reserva.sala_nome}\nStatus: ${reserva.status}`);
      }
    }
  }
</script>
