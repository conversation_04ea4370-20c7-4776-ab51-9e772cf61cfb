// scripts/createTestUser.js
require('dotenv').config();
const bcrypt = require('bcrypt');
const db = require('../config/db');

async function createTestUser() {
  try {
    await db.connect();
    console.log('Conectado ao banco de dados');

    // Verificar se o usuário de teste já existe
    const existingUser = await db.query(
      'SELECT * FROM usuarios WHERE email = $1',
      ['<EMAIL>']
    );

    if (existingUser.rows.length > 0) {
      console.log('Usuário de teste já existe!');
      console.log('Email: <EMAIL>');
      console.log('Senha: 123456');
      return;
    }

    // Criar hash da senha
    const password = '123456';
    const saltRounds = 10;
    const password_hash = await bcrypt.hash(password, saltRounds);

    // Criar usuário de teste
    const result = await db.query(`
      INSERT INTO usuarios (nome, email, password_hash, telefone, departamento, esta_ativo)
      VALUES ($1, $2, $3, $4, $5, $6)
      RETURNING *
    `, [
      'Administrador Teste',
      '<EMAIL>',
      password_hash,
      '(11) 99999-9999',
      'Administração',
      true
    ]);

    console.log('Usuário de teste criado com sucesso!');
    console.log('Email: <EMAIL>');
    console.log('Senha: 123456');
    console.log('ID:', result.rows[0].id);

    // Criar algumas salas de teste se não existirem
    await createTestRooms();

  } catch (error) {
    console.error('Erro ao criar usuário de teste:', error);
  } finally {
    process.exit(0);
  }
}

async function createTestRooms() {
  try {
    // Verificar se já existem salas
    const existingRooms = await db.query('SELECT COUNT(*) FROM salas');
    const roomCount = parseInt(existingRooms.rows[0].count);

    if (roomCount > 0) {
      console.log(`${roomCount} salas já existem no banco de dados`);
      return;
    }

    // Criar edifício de teste primeiro
    const edificioResult = await db.query(`
      INSERT INTO edificios (nome, endereco, descricao)
      VALUES ($1, $2, $3)
      RETURNING id
    `, [
      'Edifício Principal',
      'Rua Teste, 123 - Centro',
      'Edifício principal da empresa'
    ]);

    const edificioId = edificioResult.rows[0].id;

    // Criar salas de teste
    const salas = [
      {
        nome: 'Sala de Reunião A',
        floor: 2,
        capacidade: 12,
        descricao: 'Sala de reunião com projetor e quadro branco',
        tipo_sala: 'reuniao'
      },
      {
        nome: 'Auditório Principal',
        floor: 1,
        capacidade: 50,
        descricao: 'Auditório com sistema de som e projeção',
        tipo_sala: 'auditorio'
      },
      {
        nome: 'Sala de Treinamento B',
        floor: 3,
        capacidade: 20,
        descricao: 'Sala de treinamento com computadores',
        tipo_sala: 'treinamento'
      },
      {
        nome: 'Sala de Criatividade',
        floor: 3,
        capacidade: 8,
        descricao: 'Sala para brainstorming e workshops',
        tipo_sala: 'workshop'
      }
    ];

    for (const sala of salas) {
      await db.query(`
        INSERT INTO salas (nome, construcao_id, floor, capacidade, descricao, tipo_sala)
        VALUES ($1, $2, $3, $4, $5, $6)
      `, [
        sala.nome,
        edificioId,
        sala.floor,
        sala.capacidade,
        sala.descricao,
        sala.tipo_sala
      ]);
    }

    console.log(`${salas.length} salas de teste criadas com sucesso!`);

  } catch (error) {
    console.error('Erro ao criar salas de teste:', error);
  }
}

// Executar se chamado diretamente
if (require.main === module) {
  createTestUser();
}

module.exports = { createTestUser };
