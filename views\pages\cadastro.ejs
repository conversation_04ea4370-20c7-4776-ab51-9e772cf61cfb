<!DOCTYPE html>
<html lang="pt-BR">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Cadastro - Sistema de Reservas</title>
  <script src="https://cdn.tailwindcss.com"></script>
  <script src="https://unpkg.com/alpinejs@3.x.x/dist/cdn.min.js" defer></script>
  <script>
    tailwind.config = {
      theme: {
        extend: {
          colors: {
            primary: {
              50: '#eff6ff',
              500: '#3b82f6',
              600: '#2563eb',
              700: '#1d4ed8'
            }
          }
        }
      }
    }
  </script>
</head>
<body class="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100">
  <div class="min-h-screen flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8">
    <div class="max-w-md w-full space-y-8">
      <!-- Header -->
      <div class="text-center">
        <div class="mx-auto h-16 w-16 bg-primary-600 rounded-full flex items-center justify-center mb-4">
          <svg class="h-8 w-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-2m-2 0H7m5 0v-5a2 2 0 00-2-2H8a2 2 0 00-2 2v5m5 0V9a2 2 0 012-2h2a2 2 0 012 2v12"></path>
          </svg>
        </div>
        <h2 class="text-3xl font-bold text-gray-900">Criar Conta</h2>
        <p class="mt-2 text-sm text-gray-600">
          Junte-se ao Sistema de Reservas
        </p>
      </div>

      <!-- Cadastro Form -->
      <div class="bg-white rounded-lg shadow-lg p-8" x-data="cadastroData()">
        <!-- Alert Messages -->
        <div x-show="alert.show" x-transition class="mb-4 p-4 rounded-lg" :class="alert.type === 'success' ? 'bg-green-100 text-green-700' : 'bg-red-100 text-red-700'">
          <p x-text="alert.message"></p>
        </div>

        <form @submit.prevent="submitCadastro()" class="space-y-6">
          <!-- Nome Completo -->
          <div>
            <label for="nome" class="block text-sm font-medium text-gray-700 mb-2">
              Nome Completo
            </label>
            <input
              id="nome"
              name="nome"
              type="text"
              required
              x-model="form.nome"
              class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500 transition-colors"
              placeholder="Digite seu nome completo"
            >
          </div>

          <!-- Email -->
          <div>
            <label for="email" class="block text-sm font-medium text-gray-700 mb-2">
              Email
            </label>
            <input
              id="email"
              name="email"
              type="email"
              required
              x-model="form.email"
              class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500 transition-colors"
              placeholder="Digite seu email"
            >
          </div>

          <!-- Telefone -->
          <div>
            <label for="telefone" class="block text-sm font-medium text-gray-700 mb-2">
              Telefone (opcional)
            </label>
            <input
              id="telefone"
              name="telefone"
              type="tel"
              x-model="form.telefone"
              @input="formatTelefone()"
              class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500 transition-colors"
              placeholder="(11) 99999-9999"
              maxlength="15"
            >
          </div>

          <!-- Departamento -->
          <div>
            <label for="departamento" class="block text-sm font-medium text-gray-700 mb-2">
              Departamento
            </label>
            <select
              id="departamento"
              name="departamento"
              x-model="form.departamento"
              class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500 transition-colors"
            >
              <option value="">Selecione um departamento</option>
              <option value="TI">TI</option>
              <option value="RH">RH</option>
              <option value="Vendas">Vendas</option>
              <option value="Marketing">Marketing</option>
              <option value="Financeiro">Financeiro</option>
              <option value="Administração">Administração</option>
              <option value="Operações">Operações</option>
            </select>
          </div>

          <!-- Senha -->
          <div>
            <label for="password" class="block text-sm font-medium text-gray-700 mb-2">
              Senha
            </label>
            <div class="relative">
              <input
                id="password"
                name="password"
                :type="showPassword ? 'text' : 'password'"
                required
                x-model="form.password"
                class="w-full px-4 py-3 pr-12 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500 transition-colors"
                placeholder="Digite sua senha"
              >
              <button
                type="button"
                @click="showPassword = !showPassword"
                class="absolute inset-y-0 right-0 pr-3 flex items-center"
              >
                <svg x-show="!showPassword" class="h-5 w-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
                </svg>
                <svg x-show="showPassword" class="h-5 w-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13.875 18.825A10.05 10.05 0 0112 19c-4.478 0-8.268-2.943-9.543-7a9.97 9.97 0 011.563-3.029m5.858.908a3 3 0 114.243 4.243M9.878 9.878l4.242 4.242M9.878 9.878L3 3m6.878 6.878L21 21"></path>
                </svg>
              </button>
            </div>
            <!-- Password Strength Indicator -->
            <div class="mt-2">
              <div class="flex space-x-1">
                <div class="h-1 flex-1 rounded" :class="passwordStrength >= 1 ? 'bg-red-500' : 'bg-gray-200'"></div>
                <div class="h-1 flex-1 rounded" :class="passwordStrength >= 2 ? 'bg-yellow-500' : 'bg-gray-200'"></div>
                <div class="h-1 flex-1 rounded" :class="passwordStrength >= 3 ? 'bg-green-500' : 'bg-gray-200'"></div>
              </div>
              <p class="text-xs text-gray-500 mt-1" x-text="passwordStrengthText"></p>
            </div>
          </div>

          <!-- Confirmar Senha -->
          <div>
            <label for="confirmPassword" class="block text-sm font-medium text-gray-700 mb-2">
              Confirmar Senha
            </label>
            <input
              id="confirmPassword"
              name="confirmPassword"
              type="password"
              required
              x-model="form.confirmPassword"
              class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500 transition-colors"
              placeholder="Confirme sua senha"
            >
            <p x-show="form.confirmPassword && form.password !== form.confirmPassword" class="text-xs text-red-500 mt-1">
              As senhas não coincidem
            </p>
          </div>



          <!-- Submit Button -->
          <button
            type="submit"
            :disabled="loading || !isFormValid"
            class="w-full flex justify-center py-3 px-4 border border-transparent rounded-lg shadow-sm text-sm font-medium text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 disabled:bg-gray-400 disabled:cursor-not-allowed transition-colors"
          >
            <svg x-show="loading" class="animate-spin -ml-1 mr-3 h-5 w-5 text-white" fill="none" viewBox="0 0 24 24">
              <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
              <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
            </svg>
            <span x-show="!loading">Criar Conta</span>
            <span x-show="loading">Criando...</span>
          </button>
        </form>

        <!-- Login Link -->
        <div class="mt-6 text-center">
          <p class="text-sm text-gray-600">
            Já tem uma conta?
            <a href="/login" class="font-medium text-primary-600 hover:text-primary-500 transition-colors">
              Faça login
            </a>
          </p>
        </div>
      </div>
    </div>
  </div>

  <script>
    function cadastroData() {
      return {
        loading: false,
        showPassword: false,
        alert: {
          show: false,
          type: 'success',
          message: ''
        },
        form: {
          nome: '',
          email: '',
          telefone: '',
          departamento: '',
          password: '',
          confirmPassword: ''
        },

        get passwordStrength() {
          const password = this.form.password;
          if (!password) return 0;

          let strength = 0;
          if (password.length >= 6) strength++; // Reduzido de 8 para 6
          if (/[A-Z]/.test(password) || /[a-z]/.test(password)) strength++; // OR em vez de AND
          if (/\d/.test(password) || /[!@#$%^&*(),.?":{}|<>]/.test(password)) strength++; // OR em vez de AND

          return strength;
        },

        get passwordStrengthText() {
          switch (this.passwordStrength) {
            case 0: return 'Muito fraca (mínimo 6 caracteres)';
            case 1: return 'Aceitável';
            case 2: return 'Boa';
            case 3: return 'Forte';
            default: return '';
          }
        },

        get isFormValid() {
          return this.form.nome.trim() &&
                 this.form.email.trim() &&
                 this.form.password &&
                 this.form.confirmPassword &&
                 this.form.password === this.form.confirmPassword &&
                 this.passwordStrength >= 1;
        },

        showAlert(type, message) {
          this.alert = { show: true, type, message };
          setTimeout(() => {
            this.alert.show = false;
          }, 5000);
        },

        formatTelefone() {
          let value = this.form.telefone.replace(/\D/g, '');

          if (value.length <= 11) {
            if (value.length <= 2) {
              this.form.telefone = value;
            } else if (value.length <= 7) {
              this.form.telefone = `(${value.slice(0, 2)}) ${value.slice(2)}`;
            } else {
              this.form.telefone = `(${value.slice(0, 2)}) ${value.slice(2, 7)}-${value.slice(7)}`;
            }
          }
        },

        async submitCadastro() {
          if (!this.isFormValid) {
            this.showAlert('error', 'Por favor, preencha todos os campos obrigatórios corretamente.');
            return;
          }

          this.loading = true;

          try {
            const response = await fetch('/api/auth/register', {
              method: 'POST',
              headers: {
                'Content-Type': 'application/json'
              },
              body: JSON.stringify({
                nome: this.form.nome,
                email: this.form.email,
                telefone: this.form.telefone,
                departamento: this.form.departamento,
                password: this.form.password
              })
            });

            const data = await response.json();

            if (data.success) {
              this.showAlert('success', 'Conta criada com sucesso! Redirecionando...');
              setTimeout(() => {
                window.location.href = '/login?registered=true';
              }, 2000);
            } else {
              this.showAlert('error', data.message || 'Erro ao criar conta. Tente novamente.');
            }
          } catch (error) {
            console.error('Erro no cadastro:', error);
            this.showAlert('error', 'Erro de conexão. Tente novamente.');
          } finally {
            this.loading = false;
          }
        }
      }
    }
  </script>
</body>
</html>
