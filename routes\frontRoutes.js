const express = require('express');
const router = express.Router();
const path = require('path');

// Redirect root to login
router.get('/', (req, res) => {
  res.redirect('/login');
});

// Login page
router.get('/login', (req, res) => {
  res.render(path.join(__dirname, '../views/layout/main'), {
    pageTitle: 'Login - Sistema de Reservas',
    content: path.join(__dirname, '../views/pages/login')
  });
});

// Dashboard page
router.get('/dashboard', (req, res) => {
  res.render(path.join(__dirname, '../views/layout/main'), {
    pageTitle: 'Dashboard - Sistema de Reservas',
    content: path.join(__dirname, '../views/pages/dashboard')
  });
});

// Minhas Reservas page
router.get('/minhas-reservas', (req, res) => {
  res.render(path.join(__dirname, '../views/layout/main'), {
    pageTitle: 'Minhas Reservas - Sistema de Reservas',
    content: path.join(__dirname, '../views/pages/minhas-reservas')
  });
});

// Nova Reserva page
router.get('/nova-reserva', (req, res) => {
  res.render(path.join(__dirname, '../views/layout/main'), {
    pageTitle: 'Nova Reserva - Sistema de Reservas',
    content: path.join(__dirname, '../views/pages/nova-reserva')
  });
});

// Legacy routes (keeping for compatibility)
router.get('/about', (req, res) => {
  res.render(path.join(__dirname, '../views/layout/main'), {
    pageTitle: 'Sobre - Sistema de Reservas',
    content: path.join(__dirname, '../views/pages/page2')
  });
});

module.exports = router;
