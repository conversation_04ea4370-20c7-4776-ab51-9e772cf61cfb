<!-- Sidebar -->
<aside class="w-64 bg-white shadow-sm border-r border-gray-200 min-h-screen" x-data="sidebarData()">
  <nav class="p-6">
    <!-- Navigation Menu -->
    <ul class="space-y-2">
      <!-- Dashboard -->
      <li>
        <a 
          href="/dashboard" 
          class="flex items-center space-x-3 px-3 py-2 rounded-lg text-gray-700 hover:bg-gray-100 transition-colors"
          :class="{ 'bg-primary-50 text-primary-700 border-r-2 border-primary-600': currentPage === 'dashboard' }"
        >
          <svg class="h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2H5a2 2 0 00-2-2z"></path>
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 5a2 2 0 012-2h4a2 2 0 012 2v6H8V5z"></path>
          </svg>
          <span class="font-medium">Dashboard</span>
        </a>
      </li>

      <!-- Minhas Reservas -->
      <li>
        <a 
          href="/minhas-reservas" 
          class="flex items-center space-x-3 px-3 py-2 rounded-lg text-gray-700 hover:bg-gray-100 transition-colors"
          :class="{ 'bg-primary-50 text-primary-700 border-r-2 border-primary-600': currentPage === 'reservas' }"
        >
          <svg class="h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
          </svg>
          <span class="font-medium">Minhas Reservas</span>
        </a>
      </li>

      <!-- Nova Reserva -->
      <li>
        <a 
          href="/nova-reserva" 
          class="flex items-center space-x-3 px-3 py-2 rounded-lg text-gray-700 hover:bg-gray-100 transition-colors"
        >
          <svg class="h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
          </svg>
          <span class="font-medium">Nova Reserva</span>
        </a>
      </li>

      <!-- Buscar Salas -->
      <li>
        <a
          href="/buscar-salas"
          class="flex items-center space-x-3 px-3 py-2 rounded-lg text-gray-700 hover:bg-gray-100 transition-colors"
          :class="{ 'bg-primary-50 text-primary-700 border-r-2 border-primary-600': currentPage === 'buscar-salas' }"
        >
          <svg class="h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
          </svg>
          <span class="font-medium">Buscar Salas</span>
        </a>
      </li>

      <!-- Calendário -->
      <li>
        <a
          href="/calendario"
          class="flex items-center space-x-3 px-3 py-2 rounded-lg text-gray-700 hover:bg-gray-100 transition-colors"
          :class="{ 'bg-primary-50 text-primary-700 border-r-2 border-primary-600': currentPage === 'calendario' }"
        >
          <svg class="h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
          </svg>
          <span class="font-medium">Calendário</span>
        </a>
      </li>

      <!-- Divider -->
      <li class="pt-4">
        <div class="border-t border-gray-200"></div>
      </li>

      <!-- Favoritos -->
      <li>
        <a
          href="/favoritos"
          class="flex items-center space-x-3 px-3 py-2 rounded-lg text-gray-700 hover:bg-gray-100 transition-colors"
          :class="{ 'bg-primary-50 text-primary-700 border-r-2 border-primary-600': currentPage === 'favoritos' }"
        >
          <svg class="h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z"></path>
          </svg>
          <span class="font-medium">Favoritos</span>
        </a>
      </li>

      <!-- Histórico -->
      <li>
        <a
          href="/historico"
          class="flex items-center space-x-3 px-3 py-2 rounded-lg text-gray-700 hover:bg-gray-100 transition-colors"
          :class="{ 'bg-primary-50 text-primary-700 border-r-2 border-primary-600': currentPage === 'historico' }"
        >
          <svg class="h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
          </svg>
          <span class="font-medium">Histórico</span>
        </a>
      </li>

      <!-- Relatórios (Admin only) -->
      <li x-show="isAdmin">
        <a
          href="/relatorios"
          class="flex items-center space-x-3 px-3 py-2 rounded-lg text-gray-700 hover:bg-gray-100 transition-colors"
          :class="{ 'bg-primary-50 text-primary-700 border-r-2 border-primary-600': currentPage === 'relatorios' }"
        >
          <svg class="h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
          </svg>
          <span class="font-medium">Relatórios</span>
        </a>
      </li>

      <!-- Divider -->
      <li class="pt-4" x-show="isAdmin">
        <div class="border-t border-gray-200"></div>
      </li>

      <!-- Gerenciar Salas (Admin only) -->
      <li x-show="isAdmin">
        <div x-data="{ open: false }">
          <button 
            @click="open = !open"
            class="flex items-center justify-between w-full px-3 py-2 rounded-lg text-gray-700 hover:bg-gray-100 transition-colors"
          >
            <div class="flex items-center space-x-3">
              <svg class="h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-2m-2 0H7m5 0v-5a2 2 0 00-2-2H8a2 2 0 00-2 2v5m5 0V9a2 2 0 012-2h2a2 2 0 012 2v12"></path>
              </svg>
              <span class="font-medium">Gerenciar</span>
            </div>
            <svg 
              class="h-4 w-4 transition-transform"
              :class="{ 'rotate-180': open }"
              fill="none" 
              stroke="currentColor" 
              viewBox="0 0 24 24"
            >
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
            </svg>
          </button>
          
          <div x-show="open" x-transition class="ml-8 mt-2 space-y-1">
            <a href="/admin/salas" class="block px-3 py-2 text-sm text-gray-600 hover:text-gray-900 hover:bg-gray-50 rounded-lg">
              Salas
            </a>
            <a href="/admin/usuarios" class="block px-3 py-2 text-sm text-gray-600 hover:text-gray-900 hover:bg-gray-50 rounded-lg">
              Usuários
            </a>
            <a href="/admin/equipamentos" class="block px-3 py-2 text-sm text-gray-600 hover:text-gray-900 hover:bg-gray-50 rounded-lg">
              Equipamentos
            </a>
            <a href="/admin/edificios" class="block px-3 py-2 text-sm text-gray-600 hover:text-gray-900 hover:bg-gray-50 rounded-lg">
              Edifícios
            </a>
          </div>
        </div>
      </li>
    </ul>

    <!-- Quick Stats -->
    <div class="mt-8 p-4 bg-gray-50 rounded-lg">
      <h4 class="text-sm font-medium text-gray-900 mb-3">Resumo Rápido</h4>
      <div class="space-y-2">
        <div class="flex justify-between text-sm">
          <span class="text-gray-600">Reservas Hoje</span>
          <span class="font-medium text-gray-900">3</span>
        </div>
        <div class="flex justify-between text-sm">
          <span class="text-gray-600">Esta Semana</span>
          <span class="font-medium text-gray-900">12</span>
        </div>
        <div class="flex justify-between text-sm">
          <span class="text-gray-600">Salas Favoritas</span>
          <span class="font-medium text-gray-900">5</span>
        </div>
      </div>
    </div>

    <!-- Help Section -->
    <div class="mt-6 p-4 bg-blue-50 rounded-lg">
      <div class="flex items-center space-x-2 mb-2">
        <svg class="h-5 w-5 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8.228 9c.549-1.165 2.03-2 3.772-2 2.21 0 4 1.343 4 3 0 1.4-1.278 2.575-3.006 2.907-.542.104-.994.54-.994 1.093m0 3h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
        </svg>
        <h4 class="text-sm font-medium text-blue-900">Precisa de Ajuda?</h4>
      </div>
      <p class="text-xs text-blue-700 mb-3">Consulte nosso guia de uso ou entre em contato com o suporte.</p>
      <a href="/ajuda" class="text-xs text-blue-600 hover:text-blue-500 font-medium">
        Ver Guia →
      </a>
    </div>
  </nav>
</aside>

<script>
  function sidebarData() {
    return {
      currentPage: getCurrentPage(),
      isAdmin: <%= locals.currentUser && locals.currentUser.departamento === 'Administração' ? 'true' : 'false' %>
    }
  }

  function getCurrentPage() {
    const path = window.location.pathname;
    if (path === '/dashboard') return 'dashboard';
    if (path === '/minhas-reservas') return 'reservas';
    if (path === '/nova-reserva') return 'nova-reserva';
    if (path === '/buscar-salas') return 'buscar-salas';
    if (path === '/calendario') return 'calendario';
    if (path === '/favoritos') return 'favoritos';
    if (path === '/historico') return 'historico';
    if (path === '/relatorios') return 'relatorios';
    if (path === '/ajuda') return 'ajuda';
    return 'dashboard';
  }
</script>
