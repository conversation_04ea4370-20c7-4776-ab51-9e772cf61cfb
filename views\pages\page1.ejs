<h2>Página 1</h2>
<p>Lista de Usuários:</p>

<table border="1" id="usersTable">
  <thead>
    <tr>
      <th>ID</th>
      <th>Nome</th>
      <th>Email</th>
    </tr>
  </thead>
  <tbody>
    <tr>
      <td colspan="3">Carregando usuários...</td>
    </tr>
  </tbody>
</table>

<script>
  // Função para buscar usuários da API e renderizar na tabela
  async function fetchUsers() {
    try {
      const response = await fetch('/users');
      if (!response.ok) {
        throw new Error('Erro ao buscar usuários');
      }
      const users = await response.json();

      const tableBody = document.querySelector('#usersTable tbody');
      tableBody.innerHTML = ''; // Limpa o conteúdo anterior

      if (users.length > 0) {
        users.forEach(user => {
          const row = document.createElement('tr');
          row.innerHTML = `
            <td>${user.id}</td>
            <td>${user.name}</td>
            <td>${user.email}</td>
          `;
          tableBody.appendChild(row);
        });
      } else {
        tableBody.innerHTML = '<tr><td colspan="3">Nenhum usuário encontrado.</td></tr>';
      }
    } catch (error) {
      console.error(error);
      const tableBody = document.querySelector('#usersTable tbody');
      tableBody.innerHTML = '<tr><td colspan="3">Erro ao carregar usuários.</td></tr>';
    }
  }

  // Chama a função ao carregar a página
  fetchUsers();
</script>