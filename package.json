{"name": "mvc-boilerplate", "version": "1.0.0", "description": "Boilerplate para estrutura MVC em JavaScript com PostgreSQL e testes", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "test": "jest", "test:coverage": "jest --coverage", "init-db": "node scripts/runSQLScript.js", "migrate": "node-pg-migrate", "migrate:create": "node-pg-migrate create"}, "dependencies": {"bcrypt": "^6.0.0", "dotenv": "^10.0.0", "ejs": "^3.1.10", "express": "^4.21.2", "express-session": "^1.18.1", "pg": "^8.16.0"}, "devDependencies": {"jest": "^27.0.6", "node-pg-migrate": "^8.0.0", "nodemon": "^2.0.22", "supertest": "^6.1.3"}, "directories": {"test": "tests"}, "keywords": [], "author": "", "license": "ISC"}