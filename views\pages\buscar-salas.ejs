<!-- <PERSON><PERSON> Salas Page -->
<div class="min-h-screen bg-gray-50" x-data="buscarSalasData()">
  <!-- Header -->
  <%- include('../components/dashboard-header') %>
  
  <!-- Main Content -->
  <div class="flex">
    <!-- Sidebar -->
    <%- include('../components/sidebar') %>
    
    <!-- Main Content Area -->
    <main class="flex-1 p-6">
      <!-- Page Header -->
      <div class="mb-8">
        <h1 class="text-2xl font-bold text-gray-900 mb-2">Buscar Salas</h1>
        <p class="text-gray-600">Encontre a sala perfeita para sua reunião</p>
      </div>

      <!-- Search and Filters -->
      <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-6">
        <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
          <!-- Search -->
          <div class="md:col-span-2">
            <label class="block text-sm font-medium text-gray-700 mb-2">Buscar por nome</label>
            <input 
              type="text" 
              x-model="filters.search"
              placeholder="Digite o nome da sala..."
              class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
            >
          </div>

          <!-- Capacity Filter -->
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-2">Capacidade mínima</label>
            <select 
              x-model="filters.capacidade"
              class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
            >
              <option value="">Qualquer capacidade</option>
              <option value="5">5+ pessoas</option>
              <option value="10">10+ pessoas</option>
              <option value="20">20+ pessoas</option>
              <option value="50">50+ pessoas</option>
            </select>
          </div>

          <!-- Building Filter -->
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-2">Edifício</label>
            <select 
              x-model="filters.edificio"
              class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
            >
              <option value="">Todos os edifícios</option>
              <template x-for="edificio in edificios" :key="edificio.id">
                <option :value="edificio.id" x-text="edificio.nome"></option>
              </template>
            </select>
          </div>
        </div>

        <!-- Advanced Filters -->
        <div class="mt-4 pt-4 border-t border-gray-200">
          <button 
            @click="showAdvancedFilters = !showAdvancedFilters"
            class="flex items-center text-sm text-primary-600 hover:text-primary-700"
          >
            <span>Filtros avançados</span>
            <svg 
              class="ml-1 h-4 w-4 transition-transform"
              :class="{ 'rotate-180': showAdvancedFilters }"
              fill="none" 
              stroke="currentColor" 
              viewBox="0 0 24 24"
            >
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
            </svg>
          </button>

          <div x-show="showAdvancedFilters" x-transition class="mt-4 grid grid-cols-1 md:grid-cols-3 gap-4">
            <!-- Room Type -->
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-2">Tipo de sala</label>
              <select 
                x-model="filters.tipo"
                class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
              >
                <option value="">Todos os tipos</option>
                <option value="reuniao">Reunião</option>
                <option value="auditorio">Auditório</option>
                <option value="treinamento">Treinamento</option>
                <option value="workshop">Workshop</option>
              </select>
            </div>

            <!-- Floor -->
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-2">Andar</label>
              <select 
                x-model="filters.andar"
                class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
              >
                <option value="">Qualquer andar</option>
                <option value="1">1º Andar</option>
                <option value="2">2º Andar</option>
                <option value="3">3º Andar</option>
                <option value="4">4º Andar</option>
              </select>
            </div>

            <!-- Availability -->
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-2">Disponibilidade</label>
              <select 
                x-model="filters.disponibilidade"
                class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
              >
                <option value="">Todas as salas</option>
                <option value="disponivel">Disponível agora</option>
                <option value="hoje">Disponível hoje</option>
              </select>
            </div>
          </div>
        </div>
      </div>

      <!-- Results -->
      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        <template x-for="sala in filteredSalas" :key="sala.id">
          <div class="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden hover:shadow-md transition-shadow">
            <!-- Room Image -->
            <div class="h-48 bg-gradient-to-br from-blue-500 to-purple-600 relative">
              <div class="absolute inset-0 bg-black bg-opacity-20"></div>
              <div class="absolute bottom-4 left-4 text-white">
                <h3 class="text-lg font-semibold" x-text="sala.nome"></h3>
                <p class="text-sm opacity-90" x-text="sala.localizacao"></p>
              </div>
              <!-- Favorite Button -->
              <button 
                @click="toggleFavorite(sala)"
                class="absolute top-4 right-4 p-2 rounded-full bg-white bg-opacity-20 hover:bg-opacity-30 transition-colors"
              >
                <svg 
                  class="h-5 w-5 text-white"
                  :class="{ 'fill-current': sala.isFavorite }"
                  fill="none" 
                  stroke="currentColor" 
                  viewBox="0 0 24 24"
                >
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z"></path>
                </svg>
              </button>
            </div>

            <!-- Room Details -->
            <div class="p-4">
              <div class="flex items-center justify-between mb-3">
                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                  <span x-text="sala.tipo_sala"></span>
                </span>
                <span class="text-sm text-gray-500" x-text="'Capacidade: ' + sala.capacidade"></span>
              </div>

              <p class="text-sm text-gray-600 mb-4" x-text="sala.descricao"></p>

              <!-- Equipment -->
              <div class="mb-4">
                <h4 class="text-sm font-medium text-gray-900 mb-2">Equipamentos</h4>
                <div class="flex flex-wrap gap-1">
                  <template x-for="equipamento in sala.equipamentos" :key="equipamento">
                    <span class="inline-flex items-center px-2 py-1 rounded text-xs bg-gray-100 text-gray-700" x-text="equipamento"></span>
                  </template>
                </div>
              </div>

              <!-- Actions -->
              <div class="flex space-x-2">
                <button 
                  @click="reservarSala(sala)"
                  class="flex-1 bg-primary-600 hover:bg-primary-700 text-white px-4 py-2 rounded-lg text-sm font-medium transition-colors"
                >
                  Reservar
                </button>
                <button 
                  @click="verDetalhes(sala)"
                  class="px-4 py-2 border border-gray-300 text-gray-700 rounded-lg text-sm font-medium hover:bg-gray-50 transition-colors"
                >
                  Detalhes
                </button>
              </div>
            </div>
          </div>
        </template>

        <!-- Empty State -->
        <div x-show="filteredSalas.length === 0" class="col-span-full text-center py-12">
          <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-2m-2 0H7m5 0v-5a2 2 0 00-2-2H8a2 2 0 00-2 2v5m5 0V9a2 2 0 012-2h2a2 2 0 012 2v12"></path>
          </svg>
          <h3 class="mt-2 text-sm font-medium text-gray-900">Nenhuma sala encontrada</h3>
          <p class="mt-1 text-sm text-gray-500">Tente ajustar os filtros de busca.</p>
        </div>
      </div>
    </main>
  </div>
</div>

<script>
  function buscarSalasData() {
    return {
      loading: true,
      showAdvancedFilters: false,
      filters: {
        search: '',
        capacidade: '',
        edificio: '',
        tipo: '',
        andar: '',
        disponibilidade: ''
      },
      salas: [],
      edificios: [],

      async init() {
        await this.loadData();
      },

      async loadData() {
        try {
          this.loading = true;
          
          // Carregar salas e edifícios
          const [salasResponse, edificiosResponse] = await Promise.all([
            fetch('/api/salas'),
            fetch('/api/edificios')
          ]);

          if (salasResponse.ok) {
            const salasData = await salasResponse.json();
            this.salas = salasData.map(sala => ({
              ...sala,
              localizacao: `${sala.edificio_nome || 'Edifício'} - ${sala.floor}º Andar`,
              equipamentos: ['Projetor', 'Quadro', 'Wi-Fi'], // Simulado
              isFavorite: false
            }));
          }

          if (edificiosResponse.ok) {
            this.edificios = await edificiosResponse.json();
          }

        } catch (error) {
          console.error('Erro ao carregar dados:', error);
        } finally {
          this.loading = false;
        }
      },

      get filteredSalas() {
        return this.salas.filter(sala => {
          // Search filter
          if (this.filters.search && !sala.nome.toLowerCase().includes(this.filters.search.toLowerCase())) {
            return false;
          }

          // Capacity filter
          if (this.filters.capacidade && sala.capacidade < parseInt(this.filters.capacidade)) {
            return false;
          }

          // Building filter
          if (this.filters.edificio && sala.construcao_id !== this.filters.edificio) {
            return false;
          }

          // Type filter
          if (this.filters.tipo && sala.tipo_sala !== this.filters.tipo) {
            return false;
          }

          // Floor filter
          if (this.filters.andar && sala.floor !== parseInt(this.filters.andar)) {
            return false;
          }

          return true;
        });
      },

      toggleFavorite(sala) {
        sala.isFavorite = !sala.isFavorite;
        // TODO: Implementar API de favoritos
      },

      reservarSala(sala) {
        // Redirecionar para nova reserva com sala pré-selecionada
        window.location.href = `/nova-reserva?sala=${sala.id}`;
      },

      verDetalhes(sala) {
        // TODO: Implementar modal de detalhes ou página dedicada
        alert(`Detalhes da ${sala.nome}\nCapacidade: ${sala.capacidade} pessoas\nTipo: ${sala.tipo_sala}`);
      }
    }
  }
</script>
