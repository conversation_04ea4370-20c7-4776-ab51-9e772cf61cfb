<!-- Ajuda Page -->
<div class="min-h-screen bg-gray-50" x-data="ajudaData()">
  <!-- Header -->
  <%- include('../components/dashboard-header') %>
  
  <!-- Main Content -->
  <div class="flex">
    <!-- Sidebar -->
    <%- include('../components/sidebar') %>
    
    <!-- Main Content Area -->
    <main class="flex-1 p-6">
      <!-- Page Header -->
      <div class="mb-8">
        <h1 class="text-2xl font-bold text-gray-900 mb-2">Central de Ajuda</h1>
        <p class="text-gray-600">Encontre respostas para suas dúvidas sobre o sistema de reservas</p>
      </div>

      <!-- Search -->
      <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-8">
        <div class="max-w-md mx-auto">
          <div class="relative">
            <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
              <svg class="h-5 w-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
              </svg>
            </div>
            <input 
              type="text" 
              x-model="searchQuery"
              placeholder="Buscar na central de ajuda..."
              class="block w-full pl-10 pr-3 py-3 border border-gray-300 rounded-lg leading-5 bg-white placeholder-gray-500 focus:outline-none focus:placeholder-gray-400 focus:ring-1 focus:ring-primary-500 focus:border-primary-500"
            >
          </div>
        </div>
      </div>

      <!-- Quick Actions -->
      <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6 text-center">
          <div class="p-3 bg-blue-100 rounded-full w-16 h-16 mx-auto mb-4 flex items-center justify-center">
            <svg class="h-8 w-8 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C20.832 18.477 19.246 18 17.5 18c-1.746 0-3.332.477-4.5 1.253"></path>
            </svg>
          </div>
          <h3 class="text-lg font-semibold text-gray-900 mb-2">Guia do Usuário</h3>
          <p class="text-gray-600 mb-4">Manual completo de como usar o sistema</p>
          <button 
            @click="showSection('guia')"
            class="text-primary-600 hover:text-primary-700 font-medium"
          >
            Ver Guia →
          </button>
        </div>

        <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6 text-center">
          <div class="p-3 bg-green-100 rounded-full w-16 h-16 mx-auto mb-4 flex items-center justify-center">
            <svg class="h-8 w-8 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8.228 9c.549-1.165 2.03-2 3.772-2 2.21 0 4 1.343 4 3 0 1.4-1.278 2.575-3.006 2.907-.542.104-.994.54-.994 1.093m0 3h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
            </svg>
          </div>
          <h3 class="text-lg font-semibold text-gray-900 mb-2">FAQ</h3>
          <p class="text-gray-600 mb-4">Perguntas frequentes e respostas</p>
          <button 
            @click="showSection('faq')"
            class="text-primary-600 hover:text-primary-700 font-medium"
          >
            Ver FAQ →
          </button>
        </div>

        <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6 text-center">
          <div class="p-3 bg-purple-100 rounded-full w-16 h-16 mx-auto mb-4 flex items-center justify-center">
            <svg class="h-8 w-8 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"></path>
            </svg>
          </div>
          <h3 class="text-lg font-semibold text-gray-900 mb-2">Contato</h3>
          <p class="text-gray-600 mb-4">Entre em contato com o suporte</p>
          <button 
            @click="showSection('contato')"
            class="text-primary-600 hover:text-primary-700 font-medium"
          >
            Contatar →
          </button>
        </div>
      </div>

      <!-- Content Sections -->
      <div class="bg-white rounded-lg shadow-sm border border-gray-200">
        <!-- Guia do Usuário -->
        <div x-show="activeSection === 'guia'" x-transition>
          <div class="p-6 border-b border-gray-200">
            <h2 class="text-xl font-semibold text-gray-900">Guia do Usuário</h2>
          </div>
          <div class="p-6">
            <div class="space-y-6">
              <div>
                <h3 class="text-lg font-medium text-gray-900 mb-3">Como fazer uma reserva</h3>
                <ol class="list-decimal list-inside space-y-2 text-gray-600">
                  <li>Acesse a página "Nova Reserva" no menu lateral</li>
                  <li>Preencha o título da reunião</li>
                  <li>Selecione a sala desejada</li>
                  <li>Escolha a data e horário</li>
                  <li>Informe o número de participantes</li>
                  <li>Adicione uma descrição (opcional)</li>
                  <li>Clique em "Criar Reserva"</li>
                </ol>
              </div>

              <div>
                <h3 class="text-lg font-medium text-gray-900 mb-3">Como cancelar uma reserva</h3>
                <ol class="list-decimal list-inside space-y-2 text-gray-600">
                  <li>Vá para "Minhas Reservas"</li>
                  <li>Encontre a reserva que deseja cancelar</li>
                  <li>Clique no menu de ações (três pontos)</li>
                  <li>Selecione "Cancelar"</li>
                  <li>Confirme o cancelamento</li>
                </ol>
              </div>

              <div>
                <h3 class="text-lg font-medium text-gray-900 mb-3">Como buscar salas</h3>
                <ol class="list-decimal list-inside space-y-2 text-gray-600">
                  <li>Acesse "Buscar Salas" no menu</li>
                  <li>Use os filtros para refinar sua busca</li>
                  <li>Visualize as informações de cada sala</li>
                  <li>Clique em "Reservar" para fazer uma reserva</li>
                </ol>
              </div>
            </div>
          </div>
        </div>

        <!-- FAQ -->
        <div x-show="activeSection === 'faq'" x-transition>
          <div class="p-6 border-b border-gray-200">
            <h2 class="text-xl font-semibold text-gray-900">Perguntas Frequentes</h2>
          </div>
          <div class="divide-y divide-gray-200">
            <template x-for="faq in filteredFAQs" :key="faq.id">
              <div class="p-6">
                <button 
                  @click="faq.open = !faq.open"
                  class="flex justify-between items-center w-full text-left"
                >
                  <h3 class="text-lg font-medium text-gray-900" x-text="faq.pergunta"></h3>
                  <svg 
                    class="h-5 w-5 text-gray-500 transition-transform"
                    :class="{ 'rotate-180': faq.open }"
                    fill="none" 
                    stroke="currentColor" 
                    viewBox="0 0 24 24"
                  >
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                  </svg>
                </button>
                <div x-show="faq.open" x-transition class="mt-4">
                  <p class="text-gray-600" x-text="faq.resposta"></p>
                </div>
              </div>
            </template>
          </div>
        </div>

        <!-- Contato -->
        <div x-show="activeSection === 'contato'" x-transition>
          <div class="p-6 border-b border-gray-200">
            <h2 class="text-xl font-semibold text-gray-900">Entre em Contato</h2>
          </div>
          <div class="p-6">
            <div class="grid grid-cols-1 md:grid-cols-2 gap-8">
              <!-- Contact Info -->
              <div>
                <h3 class="text-lg font-medium text-gray-900 mb-4">Informações de Contato</h3>
                <div class="space-y-4">
                  <div class="flex items-center space-x-3">
                    <svg class="h-5 w-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"></path>
                    </svg>
                    <span class="text-gray-600"><EMAIL></span>
                  </div>
                  <div class="flex items-center space-x-3">
                    <svg class="h-5 w-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z"></path>
                    </svg>
                    <span class="text-gray-600">(11) 1234-5678</span>
                  </div>
                  <div class="flex items-center space-x-3">
                    <svg class="h-5 w-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                    </svg>
                    <span class="text-gray-600">Segunda a Sexta, 8h às 18h</span>
                  </div>
                </div>
              </div>

              <!-- Contact Form -->
              <div>
                <h3 class="text-lg font-medium text-gray-900 mb-4">Envie uma Mensagem</h3>
                <form @submit.prevent="enviarMensagem()" class="space-y-4">
                  <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">Assunto</label>
                    <select 
                      x-model="contato.assunto"
                      class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
                    >
                      <option value="">Selecione um assunto</option>
                      <option value="duvida">Dúvida sobre o sistema</option>
                      <option value="problema">Problema técnico</option>
                      <option value="sugestao">Sugestão</option>
                      <option value="outro">Outro</option>
                    </select>
                  </div>
                  <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">Mensagem</label>
                    <textarea 
                      x-model="contato.mensagem"
                      rows="4"
                      class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
                      placeholder="Descreva sua dúvida ou problema..."
                    ></textarea>
                  </div>
                  <button 
                    type="submit"
                    class="w-full bg-primary-600 hover:bg-primary-700 text-white px-4 py-2 rounded-lg transition-colors"
                  >
                    Enviar Mensagem
                  </button>
                </form>
              </div>
            </div>
          </div>
        </div>

        <!-- Default Content -->
        <div x-show="activeSection === 'default'" x-transition>
          <div class="p-6">
            <div class="text-center py-12">
              <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8.228 9c.549-1.165 2.03-2 3.772-2 2.21 0 4 1.343 4 3 0 1.4-1.278 2.575-3.006 2.907-.542.104-.994.54-.994 1.093m0 3h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
              </svg>
              <h3 class="mt-2 text-sm font-medium text-gray-900">Como podemos ajudar?</h3>
              <p class="mt-1 text-sm text-gray-500">Selecione uma das opções acima ou use a busca para encontrar o que precisa.</p>
            </div>
          </div>
        </div>
      </div>
    </main>
  </div>
</div>

<script>
  function ajudaData() {
    return {
      activeSection: 'default',
      searchQuery: '',
      contato: {
        assunto: '',
        mensagem: ''
      },
      faqs: [
        {
          id: 1,
          pergunta: 'Como posso fazer uma reserva?',
          resposta: 'Para fazer uma reserva, acesse o menu "Nova Reserva", preencha os dados necessários como título, sala, data e horário, e clique em "Criar Reserva".',
          open: false
        },
        {
          id: 2,
          pergunta: 'Posso cancelar uma reserva?',
          resposta: 'Sim, você pode cancelar suas reservas acessando "Minhas Reservas", encontrando a reserva desejada e clicando em "Cancelar" no menu de ações.',
          open: false
        },
        {
          id: 3,
          pergunta: 'Como vejo a disponibilidade de uma sala?',
          resposta: 'Acesse "Buscar Salas" para ver todas as salas disponíveis. Você pode usar filtros para encontrar salas específicas e ver suas informações detalhadas.',
          open: false
        },
        {
          id: 4,
          pergunta: 'Posso fazer reservas recorrentes?',
          resposta: 'Sim, ao criar uma nova reserva, marque a opção "Esta é uma reserva recorrente" e escolha o padrão de recorrência (diário, semanal ou mensal).',
          open: false
        },
        {
          id: 5,
          pergunta: 'Como adiciono uma sala aos favoritos?',
          resposta: 'Na página "Buscar Salas", clique no ícone de coração na sala desejada. Suas salas favoritas aparecerão na página "Favoritos" para acesso rápido.',
          open: false
        },
        {
          id: 6,
          pergunta: 'Onde vejo meu histórico de reservas?',
          resposta: 'Acesse a página "Histórico" no menu lateral para ver todas as suas reservas passadas, com filtros por período, status e sala.',
          open: false
        }
      ],

      get filteredFAQs() {
        if (!this.searchQuery) return this.faqs;
        
        return this.faqs.filter(faq => 
          faq.pergunta.toLowerCase().includes(this.searchQuery.toLowerCase()) ||
          faq.resposta.toLowerCase().includes(this.searchQuery.toLowerCase())
        );
      },

      showSection(section) {
        this.activeSection = section;
      },

      enviarMensagem() {
        if (!this.contato.assunto || !this.contato.mensagem) {
          alert('Por favor, preencha todos os campos.');
          return;
        }

        // TODO: Implementar envio de mensagem
        alert('Mensagem enviada com sucesso! Entraremos em contato em breve.');
        
        // Reset form
        this.contato = {
          assunto: '',
          mensagem: ''
        };
      }
    }
  }
</script>
