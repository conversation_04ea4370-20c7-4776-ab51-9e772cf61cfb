<!-- Ad<PERSON> Page -->
<div class="min-h-screen bg-gray-50" x-data="adminSalasData()">
  <!-- Header -->
  <%- include('../components/dashboard-header') %>
  
  <!-- Main Content -->
  <div class="flex">
    <!-- Sidebar -->
    <%- include('../components/sidebar') %>
    
    <!-- Main Content Area -->
    <main class="flex-1 p-6">
      <!-- Page Header -->
      <div class="mb-8">
        <div class="flex items-center justify-between">
          <div>
            <h1 class="text-2xl font-bold text-gray-900 mb-2">Gerenciar Salas</h1>
            <p class="text-gray-600">Administre todas as salas do sistema</p>
          </div>
          <button 
            @click="showCreateModal = true"
            class="bg-primary-600 hover:bg-primary-700 text-white px-4 py-2 rounded-lg flex items-center space-x-2 transition-colors"
          >
            <svg class="h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
            </svg>
            <span>Nova Sala</span>
          </button>
        </div>
      </div>

      <!-- Filters -->
      <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-4 mb-6">
        <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
          <div>
            <input 
              type="text" 
              x-model="filters.search"
              placeholder="Buscar salas..."
              class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
            >
          </div>
          <div>
            <select 
              x-model="filters.edificio"
              class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
            >
              <option value="">Todos os edifícios</option>
              <template x-for="edificio in edificios" :key="edificio.id">
                <option :value="edificio.id" x-text="edificio.nome"></option>
              </template>
            </select>
          </div>
          <div>
            <select 
              x-model="filters.tipo"
              class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
            >
              <option value="">Todos os tipos</option>
              <option value="reuniao">Reunião</option>
              <option value="auditorio">Auditório</option>
              <option value="treinamento">Treinamento</option>
              <option value="workshop">Workshop</option>
            </select>
          </div>
          <div>
            <select 
              x-model="filters.status"
              class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
            >
              <option value="">Todos os status</option>
              <option value="true">Ativa</option>
              <option value="false">Inativa</option>
            </select>
          </div>
        </div>
      </div>

      <!-- Salas Table -->
      <div class="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
        <div class="overflow-x-auto">
          <table class="min-w-full divide-y divide-gray-200">
            <thead class="bg-gray-50">
              <tr>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Sala</th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Edifício</th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Capacidade</th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Tipo</th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Ações</th>
              </tr>
            </thead>
            <tbody class="bg-white divide-y divide-gray-200">
              <template x-for="sala in filteredSalas" :key="sala.id">
                <tr class="hover:bg-gray-50">
                  <td class="px-6 py-4 whitespace-nowrap">
                    <div>
                      <div class="text-sm font-medium text-gray-900" x-text="sala.nome"></div>
                      <div class="text-sm text-gray-500" x-text="'Andar ' + sala.floor"></div>
                    </div>
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900" x-text="sala.edificio_nome"></td>
                  <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900" x-text="sala.capacidade + ' pessoas'"></td>
                  <td class="px-6 py-4 whitespace-nowrap">
                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800" x-text="sala.tipo_sala"></span>
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap">
                    <span 
                      class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium"
                      :class="sala.esta_ativa ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'"
                      x-text="sala.esta_ativa ? 'Ativa' : 'Inativa'"
                    ></span>
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap text-sm font-medium space-x-2">
                    <button 
                      @click="editSala(sala)"
                      class="text-indigo-600 hover:text-indigo-900"
                    >
                      Editar
                    </button>
                    <button 
                      @click="toggleSalaStatus(sala)"
                      class="text-yellow-600 hover:text-yellow-900"
                      x-text="sala.esta_ativa ? 'Desativar' : 'Ativar'"
                    ></button>
                    <button 
                      @click="deleteSala(sala)"
                      class="text-red-600 hover:text-red-900"
                    >
                      Excluir
                    </button>
                  </td>
                </tr>
              </template>
            </tbody>
          </table>
        </div>

        <!-- Empty State -->
        <div x-show="filteredSalas.length === 0" class="text-center py-12">
          <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-2m-2 0H7m5 0v-5a2 2 0 00-2-2H8a2 2 0 00-2 2v5m5 0V9a2 2 0 012-2h2a2 2 0 012 2v12"></path>
          </svg>
          <h3 class="mt-2 text-sm font-medium text-gray-900">Nenhuma sala encontrada</h3>
          <p class="mt-1 text-sm text-gray-500">Comece criando uma nova sala.</p>
        </div>
      </div>

      <!-- Create/Edit Modal -->
      <div 
        x-show="showCreateModal || showEditModal" 
        x-transition:enter="ease-out duration-300"
        x-transition:enter-start="opacity-0"
        x-transition:enter-end="opacity-100"
        x-transition:leave="ease-in duration-200"
        x-transition:leave-start="opacity-100"
        x-transition:leave-end="opacity-0"
        class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50"
      >
        <div class="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
          <div class="mt-3">
            <h3 class="text-lg font-medium text-gray-900 mb-4" x-text="showCreateModal ? 'Nova Sala' : 'Editar Sala'"></h3>
            
            <form @submit.prevent="showCreateModal ? createSala() : updateSala()" class="space-y-4">
              <div>
                <label class="block text-sm font-medium text-gray-700 mb-1">Nome da Sala</label>
                <input 
                  type="text" 
                  x-model="salaForm.nome"
                  required
                  class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
                >
              </div>

              <div>
                <label class="block text-sm font-medium text-gray-700 mb-1">Edifício</label>
                <select 
                  x-model="salaForm.construcao_id"
                  required
                  class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
                >
                  <option value="">Selecione um edifício</option>
                  <template x-for="edificio in edificios" :key="edificio.id">
                    <option :value="edificio.id" x-text="edificio.nome"></option>
                  </template>
                </select>
              </div>

              <div class="grid grid-cols-2 gap-4">
                <div>
                  <label class="block text-sm font-medium text-gray-700 mb-1">Andar</label>
                  <input 
                    type="number" 
                    x-model="salaForm.floor"
                    required
                    min="1"
                    class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
                  >
                </div>

                <div>
                  <label class="block text-sm font-medium text-gray-700 mb-1">Capacidade</label>
                  <input 
                    type="number" 
                    x-model="salaForm.capacidade"
                    required
                    min="1"
                    class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
                  >
                </div>
              </div>

              <div>
                <label class="block text-sm font-medium text-gray-700 mb-1">Tipo de Sala</label>
                <select 
                  x-model="salaForm.tipo_sala"
                  required
                  class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
                >
                  <option value="">Selecione um tipo</option>
                  <option value="reuniao">Reunião</option>
                  <option value="auditorio">Auditório</option>
                  <option value="treinamento">Treinamento</option>
                  <option value="workshop">Workshop</option>
                </select>
              </div>

              <div>
                <label class="block text-sm font-medium text-gray-700 mb-1">Descrição</label>
                <textarea 
                  x-model="salaForm.descricao"
                  rows="3"
                  class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
                ></textarea>
              </div>

              <div class="flex items-center">
                <input 
                  type="checkbox" 
                  x-model="salaForm.esta_ativa"
                  class="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"
                >
                <label class="ml-2 block text-sm text-gray-900">Sala ativa</label>
              </div>

              <div class="flex justify-end space-x-3 pt-4">
                <button 
                  type="button"
                  @click="closeModal()"
                  class="px-4 py-2 text-sm font-medium text-gray-700 bg-gray-100 hover:bg-gray-200 rounded-lg transition-colors"
                >
                  Cancelar
                </button>
                <button 
                  type="submit"
                  :disabled="loading"
                  class="px-4 py-2 text-sm font-medium text-white bg-primary-600 hover:bg-primary-700 disabled:bg-gray-400 rounded-lg transition-colors"
                >
                  <span x-show="!loading" x-text="showCreateModal ? 'Criar Sala' : 'Salvar Alterações'"></span>
                  <span x-show="loading">Salvando...</span>
                </button>
              </div>
            </form>
          </div>
        </div>
      </div>
    </main>
  </div>
</div>

<script>
  function adminSalasData() {
    return {
      loading: false,
      showCreateModal: false,
      showEditModal: false,
      salas: [],
      edificios: [],
      filters: {
        search: '',
        edificio: '',
        tipo: '',
        status: ''
      },
      salaForm: {
        nome: '',
        construcao_id: '',
        floor: '',
        capacidade: '',
        tipo_sala: '',
        descricao: '',
        esta_ativa: true
      },
      editingSala: null,

      async init() {
        await this.loadData();
      },

      async loadData() {
        try {
          const [salasResponse, edificiosResponse] = await Promise.all([
            fetch('/api/salas'),
            fetch('/api/edificios')
          ]);

          if (salasResponse.ok) {
            this.salas = await salasResponse.json();
          }

          if (edificiosResponse.ok) {
            this.edificios = await edificiosResponse.json();
          }
        } catch (error) {
          console.error('Erro ao carregar dados:', error);
        }
      },

      get filteredSalas() {
        return this.salas.filter(sala => {
          if (this.filters.search && !sala.nome.toLowerCase().includes(this.filters.search.toLowerCase())) {
            return false;
          }
          if (this.filters.edificio && sala.construcao_id != this.filters.edificio) {
            return false;
          }
          if (this.filters.tipo && sala.tipo_sala !== this.filters.tipo) {
            return false;
          }
          if (this.filters.status && sala.esta_ativa.toString() !== this.filters.status) {
            return false;
          }
          return true;
        });
      },

      async createSala() {
        this.loading = true;
        try {
          const response = await fetch('/api/salas', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json'
            },
            body: JSON.stringify(this.salaForm)
          });

          const result = await response.json();
          
          if (response.ok) {
            alert('Sala criada com sucesso!');
            this.closeModal();
            await this.loadData();
          } else {
            alert(result.message || 'Erro ao criar sala');
          }
        } catch (error) {
          console.error('Erro ao criar sala:', error);
          alert('Erro de conexão');
        } finally {
          this.loading = false;
        }
      },

      editSala(sala) {
        this.editingSala = sala;
        this.salaForm = { ...sala };
        this.showEditModal = true;
      },

      async updateSala() {
        this.loading = true;
        try {
          const response = await fetch(`/api/salas/${this.editingSala.id}`, {
            method: 'PUT',
            headers: {
              'Content-Type': 'application/json'
            },
            body: JSON.stringify(this.salaForm)
          });

          const result = await response.json();
          
          if (response.ok) {
            alert('Sala atualizada com sucesso!');
            this.closeModal();
            await this.loadData();
          } else {
            alert(result.message || 'Erro ao atualizar sala');
          }
        } catch (error) {
          console.error('Erro ao atualizar sala:', error);
          alert('Erro de conexão');
        } finally {
          this.loading = false;
        }
      },

      async toggleSalaStatus(sala) {
        try {
          const response = await fetch(`/api/salas/${sala.id}`, {
            method: 'PUT',
            headers: {
              'Content-Type': 'application/json'
            },
            body: JSON.stringify({
              ...sala,
              esta_ativa: !sala.esta_ativa
            })
          });

          if (response.ok) {
            sala.esta_ativa = !sala.esta_ativa;
            alert(`Sala ${sala.esta_ativa ? 'ativada' : 'desativada'} com sucesso!`);
          } else {
            alert('Erro ao alterar status da sala');
          }
        } catch (error) {
          console.error('Erro ao alterar status:', error);
          alert('Erro de conexão');
        }
      },

      async deleteSala(sala) {
        if (confirm(`Tem certeza que deseja excluir a sala "${sala.nome}"?`)) {
          try {
            const response = await fetch(`/api/salas/${sala.id}`, {
              method: 'DELETE'
            });

            if (response.ok) {
              alert('Sala excluída com sucesso!');
              await this.loadData();
            } else {
              alert('Erro ao excluir sala');
            }
          } catch (error) {
            console.error('Erro ao excluir sala:', error);
            alert('Erro de conexão');
          }
        }
      },

      closeModal() {
        this.showCreateModal = false;
        this.showEditModal = false;
        this.editingSala = null;
        this.salaForm = {
          nome: '',
          construcao_id: '',
          floor: '',
          capacidade: '',
          tipo_sala: '',
          descricao: '',
          esta_ativa: true
        };
      }
    }
  }
</script>
