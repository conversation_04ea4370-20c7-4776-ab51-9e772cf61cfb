<!-- Admin <PERSON> Page -->
<div class="min-h-screen bg-gray-50" x-data="adminUsuariosData()">
  <!-- Header -->
  <%- include('../components/dashboard-header') %>
  
  <!-- Main Content -->
  <div class="flex">
    <!-- Sidebar -->
    <%- include('../components/sidebar') %>
    
    <!-- Main Content Area -->
    <main class="flex-1 p-6">
      <!-- Page Header -->
      <div class="mb-8">
        <div class="flex items-center justify-between">
          <div>
            <h1 class="text-2xl font-bold text-gray-900 mb-2">Gerenciar Usuários</h1>
            <p class="text-gray-600">Administre todos os usuários do sistema</p>
          </div>
          <button 
            @click="showCreateModal = true"
            class="bg-primary-600 hover:bg-primary-700 text-white px-4 py-2 rounded-lg flex items-center space-x-2 transition-colors"
          >
            <svg class="h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
            </svg>
            <span>Novo Usuário</span>
          </button>
        </div>
      </div>

      <!-- Stats Cards -->
      <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <div class="flex items-center">
            <div class="p-2 bg-blue-100 rounded-lg">
              <svg class="h-6 w-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM9 3a2 2 0 11-4 0 2 2 0 014 0z"></path>
              </svg>
            </div>
            <div class="ml-4">
              <p class="text-sm font-medium text-gray-600">Total de Usuários</p>
              <p class="text-2xl font-bold text-gray-900" x-text="stats.total"></p>
            </div>
          </div>
        </div>

        <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <div class="flex items-center">
            <div class="p-2 bg-green-100 rounded-lg">
              <svg class="h-6 w-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
              </svg>
            </div>
            <div class="ml-4">
              <p class="text-sm font-medium text-gray-600">Usuários Ativos</p>
              <p class="text-2xl font-bold text-gray-900" x-text="stats.ativos"></p>
            </div>
          </div>
        </div>

        <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <div class="flex items-center">
            <div class="p-2 bg-yellow-100 rounded-lg">
              <svg class="h-6 w-6 text-yellow-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
              </svg>
            </div>
            <div class="ml-4">
              <p class="text-sm font-medium text-gray-600">Online Hoje</p>
              <p class="text-2xl font-bold text-gray-900" x-text="stats.onlineHoje"></p>
            </div>
          </div>
        </div>

        <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <div class="flex items-center">
            <div class="p-2 bg-purple-100 rounded-lg">
              <svg class="h-6 w-6 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-2m-2 0H7m5 0v-5a2 2 0 00-2-2H8a2 2 0 00-2 2v5m5 0V9a2 2 0 012-2h2a2 2 0 012 2v12"></path>
              </svg>
            </div>
            <div class="ml-4">
              <p class="text-sm font-medium text-gray-600">Novos Este Mês</p>
              <p class="text-2xl font-bold text-gray-900" x-text="stats.novosMes"></p>
            </div>
          </div>
        </div>
      </div>

      <!-- Filters -->
      <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-4 mb-6">
        <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
          <div>
            <input 
              type="text" 
              x-model="filters.search"
              placeholder="Buscar usuários..."
              class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
            >
          </div>
          <div>
            <select 
              x-model="filters.departamento"
              class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
            >
              <option value="">Todos os departamentos</option>
              <option value="TI">TI</option>
              <option value="RH">RH</option>
              <option value="Vendas">Vendas</option>
              <option value="Marketing">Marketing</option>
              <option value="Financeiro">Financeiro</option>
              <option value="Administração">Administração</option>
            </select>
          </div>
          <div>
            <select 
              x-model="filters.status"
              class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
            >
              <option value="">Todos os status</option>
              <option value="true">Ativo</option>
              <option value="false">Inativo</option>
            </select>
          </div>
          <div>
            <select 
              x-model="filters.role"
              class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
            >
              <option value="">Todas as funções</option>
              <option value="admin">Administrador</option>
              <option value="user">Usuário</option>
            </select>
          </div>
        </div>
      </div>

      <!-- Users Table -->
      <div class="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
        <div class="overflow-x-auto">
          <table class="min-w-full divide-y divide-gray-200">
            <thead class="bg-gray-50">
              <tr>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Usuário</th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Departamento</th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Último Login</th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Função</th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Ações</th>
              </tr>
            </thead>
            <tbody class="bg-white divide-y divide-gray-200">
              <template x-for="usuario in filteredUsuarios" :key="usuario.id">
                <tr class="hover:bg-gray-50">
                  <td class="px-6 py-4 whitespace-nowrap">
                    <div class="flex items-center">
                      <div class="h-10 w-10 bg-gray-300 rounded-full flex items-center justify-center">
                        <span class="text-sm font-medium text-gray-700" x-text="getInitials(usuario.nome)"></span>
                      </div>
                      <div class="ml-4">
                        <div class="text-sm font-medium text-gray-900" x-text="usuario.nome"></div>
                        <div class="text-sm text-gray-500" x-text="usuario.email"></div>
                      </div>
                    </div>
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900" x-text="usuario.departamento || 'N/A'"></td>
                  <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500" x-text="formatDate(usuario.ultimo_login)"></td>
                  <td class="px-6 py-4 whitespace-nowrap">
                    <span 
                      class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium"
                      :class="usuario.esta_ativo ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'"
                      x-text="usuario.esta_ativo ? 'Ativo' : 'Inativo'"
                    ></span>
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap">
                    <span 
                      class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium"
                      :class="usuario.role === 'admin' ? 'bg-purple-100 text-purple-800' : 'bg-blue-100 text-blue-800'"
                      x-text="usuario.role === 'admin' ? 'Admin' : 'Usuário'"
                    ></span>
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap text-sm font-medium space-x-2">
                    <button 
                      @click="editUsuario(usuario)"
                      class="text-indigo-600 hover:text-indigo-900"
                    >
                      Editar
                    </button>
                    <button 
                      @click="toggleUsuarioStatus(usuario)"
                      class="text-yellow-600 hover:text-yellow-900"
                      x-text="usuario.esta_ativo ? 'Desativar' : 'Ativar'"
                    ></button>
                    <button 
                      @click="resetPassword(usuario)"
                      class="text-green-600 hover:text-green-900"
                    >
                      Reset Senha
                    </button>
                  </td>
                </tr>
              </template>
            </tbody>
          </table>
        </div>

        <!-- Empty State -->
        <div x-show="filteredUsuarios.length === 0" class="text-center py-12">
          <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM9 3a2 2 0 11-4 0 2 2 0 014 0z"></path>
          </svg>
          <h3 class="mt-2 text-sm font-medium text-gray-900">Nenhum usuário encontrado</h3>
          <p class="mt-1 text-sm text-gray-500">Tente ajustar os filtros de busca.</p>
        </div>
      </div>

      <!-- Create/Edit Modal -->
      <div 
        x-show="showCreateModal || showEditModal" 
        x-transition:enter="ease-out duration-300"
        x-transition:enter-start="opacity-0"
        x-transition:enter-end="opacity-100"
        x-transition:leave="ease-in duration-200"
        x-transition:leave-start="opacity-100"
        x-transition:leave-end="opacity-0"
        class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50"
      >
        <div class="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
          <div class="mt-3">
            <h3 class="text-lg font-medium text-gray-900 mb-4" x-text="showCreateModal ? 'Novo Usuário' : 'Editar Usuário'"></h3>
            
            <form @submit.prevent="showCreateModal ? createUsuario() : updateUsuario()" class="space-y-4">
              <div>
                <label class="block text-sm font-medium text-gray-700 mb-1">Nome Completo</label>
                <input 
                  type="text" 
                  x-model="usuarioForm.nome"
                  required
                  class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
                >
              </div>

              <div>
                <label class="block text-sm font-medium text-gray-700 mb-1">Email</label>
                <input 
                  type="email" 
                  x-model="usuarioForm.email"
                  required
                  class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
                >
              </div>

              <div x-show="showCreateModal">
                <label class="block text-sm font-medium text-gray-700 mb-1">Senha</label>
                <input 
                  type="password" 
                  x-model="usuarioForm.password"
                  :required="showCreateModal"
                  class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
                >
              </div>

              <div>
                <label class="block text-sm font-medium text-gray-700 mb-1">Telefone</label>
                <input 
                  type="tel" 
                  x-model="usuarioForm.telefone"
                  class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
                >
              </div>

              <div>
                <label class="block text-sm font-medium text-gray-700 mb-1">Departamento</label>
                <select 
                  x-model="usuarioForm.departamento"
                  class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
                >
                  <option value="">Selecione um departamento</option>
                  <option value="TI">TI</option>
                  <option value="RH">RH</option>
                  <option value="Vendas">Vendas</option>
                  <option value="Marketing">Marketing</option>
                  <option value="Financeiro">Financeiro</option>
                  <option value="Administração">Administração</option>
                </select>
              </div>

              <div>
                <label class="block text-sm font-medium text-gray-700 mb-1">Função</label>
                <select 
                  x-model="usuarioForm.role"
                  class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
                >
                  <option value="user">Usuário</option>
                  <option value="admin">Administrador</option>
                </select>
              </div>

              <div class="flex items-center">
                <input 
                  type="checkbox" 
                  x-model="usuarioForm.esta_ativo"
                  class="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"
                >
                <label class="ml-2 block text-sm text-gray-900">Usuário ativo</label>
              </div>

              <div class="flex justify-end space-x-3 pt-4">
                <button 
                  type="button"
                  @click="closeModal()"
                  class="px-4 py-2 text-sm font-medium text-gray-700 bg-gray-100 hover:bg-gray-200 rounded-lg transition-colors"
                >
                  Cancelar
                </button>
                <button 
                  type="submit"
                  :disabled="loading"
                  class="px-4 py-2 text-sm font-medium text-white bg-primary-600 hover:bg-primary-700 disabled:bg-gray-400 rounded-lg transition-colors"
                >
                  <span x-show="!loading" x-text="showCreateModal ? 'Criar Usuário' : 'Salvar Alterações'"></span>
                  <span x-show="loading">Salvando...</span>
                </button>
              </div>
            </form>
          </div>
        </div>
      </div>
    </main>
  </div>
</div>

<script>
  function adminUsuariosData() {
    return {
      loading: false,
      showCreateModal: false,
      showEditModal: false,
      usuarios: [],
      filters: {
        search: '',
        departamento: '',
        status: '',
        role: ''
      },
      usuarioForm: {
        nome: '',
        email: '',
        password: '',
        telefone: '',
        departamento: '',
        role: 'user',
        esta_ativo: true
      },
      editingUsuario: null,
      stats: {
        total: 0,
        ativos: 0,
        onlineHoje: 0,
        novosMes: 0
      },

      async init() {
        await this.loadData();
      },

      async loadData() {
        try {
          const response = await fetch('/api/users');
          if (response.ok) {
            this.usuarios = await response.json();
            this.calculateStats();
          }
        } catch (error) {
          console.error('Erro ao carregar usuários:', error);
        }
      },

      calculateStats() {
        this.stats.total = this.usuarios.length;
        this.stats.ativos = this.usuarios.filter(u => u.esta_ativo).length;
        
        const hoje = new Date();
        const mesPassado = new Date(hoje);
        mesPassado.setMonth(hoje.getMonth() - 1);
        
        this.stats.onlineHoje = Math.floor(this.stats.ativos * 0.3); // Simulado
        this.stats.novosMes = this.usuarios.filter(u => 
          new Date(u.criado_em) >= mesPassado
        ).length;
      },

      get filteredUsuarios() {
        return this.usuarios.filter(usuario => {
          if (this.filters.search) {
            const search = this.filters.search.toLowerCase();
            if (!usuario.nome.toLowerCase().includes(search) && 
                !usuario.email.toLowerCase().includes(search)) {
              return false;
            }
          }
          if (this.filters.departamento && usuario.departamento !== this.filters.departamento) {
            return false;
          }
          if (this.filters.status && usuario.esta_ativo.toString() !== this.filters.status) {
            return false;
          }
          if (this.filters.role && usuario.role !== this.filters.role) {
            return false;
          }
          return true;
        });
      },

      async createUsuario() {
        this.loading = true;
        try {
          const response = await fetch('/api/users', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json'
            },
            body: JSON.stringify(this.usuarioForm)
          });

          const result = await response.json();
          
          if (response.ok) {
            alert('Usuário criado com sucesso!');
            this.closeModal();
            await this.loadData();
          } else {
            alert(result.message || 'Erro ao criar usuário');
          }
        } catch (error) {
          console.error('Erro ao criar usuário:', error);
          alert('Erro de conexão');
        } finally {
          this.loading = false;
        }
      },

      editUsuario(usuario) {
        this.editingUsuario = usuario;
        this.usuarioForm = { ...usuario };
        delete this.usuarioForm.password; // Não incluir senha na edição
        this.showEditModal = true;
      },

      async updateUsuario() {
        this.loading = true;
        try {
          const response = await fetch(`/api/users/${this.editingUsuario.id}`, {
            method: 'PUT',
            headers: {
              'Content-Type': 'application/json'
            },
            body: JSON.stringify(this.usuarioForm)
          });

          const result = await response.json();
          
          if (response.ok) {
            alert('Usuário atualizado com sucesso!');
            this.closeModal();
            await this.loadData();
          } else {
            alert(result.message || 'Erro ao atualizar usuário');
          }
        } catch (error) {
          console.error('Erro ao atualizar usuário:', error);
          alert('Erro de conexão');
        } finally {
          this.loading = false;
        }
      },

      async toggleUsuarioStatus(usuario) {
        try {
          const response = await fetch(`/api/users/${usuario.id}`, {
            method: 'PUT',
            headers: {
              'Content-Type': 'application/json'
            },
            body: JSON.stringify({
              ...usuario,
              esta_ativo: !usuario.esta_ativo
            })
          });

          if (response.ok) {
            usuario.esta_ativo = !usuario.esta_ativo;
            this.calculateStats();
            alert(`Usuário ${usuario.esta_ativo ? 'ativado' : 'desativado'} com sucesso!`);
          } else {
            alert('Erro ao alterar status do usuário');
          }
        } catch (error) {
          console.error('Erro ao alterar status:', error);
          alert('Erro de conexão');
        }
      },

      async resetPassword(usuario) {
        if (confirm(`Tem certeza que deseja resetar a senha de "${usuario.nome}"?`)) {
          try {
            const response = await fetch(`/api/users/${usuario.id}/reset-password`, {
              method: 'POST'
            });

            const result = await response.json();
            
            if (response.ok) {
              alert(`Senha resetada! Nova senha: ${result.newPassword}`);
            } else {
              alert('Erro ao resetar senha');
            }
          } catch (error) {
            console.error('Erro ao resetar senha:', error);
            alert('Erro de conexão');
          }
        }
      },

      closeModal() {
        this.showCreateModal = false;
        this.showEditModal = false;
        this.editingUsuario = null;
        this.usuarioForm = {
          nome: '',
          email: '',
          password: '',
          telefone: '',
          departamento: '',
          role: 'user',
          esta_ativo: true
        };
      },

      getInitials(nome) {
        if (!nome) return 'U';
        return nome.split(' ').map(n => n.charAt(0)).join('').toUpperCase().slice(0, 2);
      },

      formatDate(dateString) {
        if (!dateString) return 'Nunca';
        return new Date(dateString).toLocaleDateString('pt-BR');
      }
    }
  }
</script>
