<!DOCTYPE html>
<html lang="pt-BR">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Esqueceu a Senha - Sistema de Reservas</title>
  <script src="https://cdn.tailwindcss.com"></script>
  <script src="https://unpkg.com/alpinejs@3.x.x/dist/cdn.min.js" defer></script>
  <script>
    tailwind.config = {
      theme: {
        extend: {
          colors: {
            primary: {
              50: '#eff6ff',
              500: '#3b82f6',
              600: '#2563eb',
              700: '#1d4ed8'
            }
          }
        }
      }
    }
  </script>
</head>
<body class="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100">
  <div class="min-h-screen flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8">
    <div class="max-w-md w-full space-y-8">
      <!-- Header -->
      <div class="text-center">
        <div class="mx-auto h-16 w-16 bg-primary-600 rounded-full flex items-center justify-center mb-4">
          <svg class="h-8 w-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 7a2 2 0 012 2m4 0a6 6 0 01-7.743 5.743L11 17H9v2H7v2H4a1 1 0 01-1-1v-2.586a1 1 0 01.293-.707l5.964-5.964A6 6 0 1121 9z"></path>
          </svg>
        </div>
        <h2 class="text-3xl font-bold text-gray-900">Esqueceu a Senha?</h2>
        <p class="mt-2 text-sm text-gray-600">
          Não se preocupe, vamos ajudá-lo a recuperar o acesso
        </p>
      </div>

      <!-- Reset Password Form -->
      <div class="bg-white rounded-lg shadow-lg p-8" x-data="esqueceuSenhaData()">
        <!-- Step 1: Email Input -->
        <div x-show="step === 1" x-transition>
          <!-- Alert Messages -->
          <div x-show="alert.show" x-transition class="mb-4 p-4 rounded-lg" :class="alert.type === 'success' ? 'bg-green-100 text-green-700' : 'bg-red-100 text-red-700'">
            <p x-text="alert.message"></p>
          </div>

          <form @submit.prevent="sendResetEmail()" class="space-y-6">
            <div>
              <label for="email" class="block text-sm font-medium text-gray-700 mb-2">
                Email
              </label>
              <input
                id="email"
                name="email"
                type="email"
                required
                x-model="email"
                class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500 transition-colors"
                placeholder="Digite seu email cadastrado"
              >
              <p class="text-xs text-gray-500 mt-2">
                Enviaremos um código de verificação para este email
              </p>
            </div>

            <button
              type="submit"
              :disabled="loading || !email"
              class="w-full flex justify-center py-3 px-4 border border-transparent rounded-lg shadow-sm text-sm font-medium text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 disabled:bg-gray-400 disabled:cursor-not-allowed transition-colors"
            >
              <svg x-show="loading" class="animate-spin -ml-1 mr-3 h-5 w-5 text-white" fill="none" viewBox="0 0 24 24">
                <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
              </svg>
              <span x-show="!loading">Enviar Código</span>
              <span x-show="loading">Enviando...</span>
            </button>
          </form>
        </div>

        <!-- Step 2: Code Verification -->
        <div x-show="step === 2" x-transition>
          <div class="text-center mb-6">
            <div class="mx-auto h-12 w-12 bg-green-100 rounded-full flex items-center justify-center mb-4">
              <svg class="h-6 w-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"></path>
              </svg>
            </div>
            <h3 class="text-lg font-medium text-gray-900">Código Enviado!</h3>
            <p class="text-sm text-gray-600 mt-1">
              Enviamos um código de 6 dígitos para <span class="font-medium" x-text="email"></span>
            </p>
          </div>

          <!-- Alert Messages -->
          <div x-show="alert.show" x-transition class="mb-4 p-4 rounded-lg" :class="alert.type === 'success' ? 'bg-green-100 text-green-700' : 'bg-red-100 text-red-700'">
            <p x-text="alert.message"></p>
          </div>

          <form @submit.prevent="verifyCode()" class="space-y-6">
            <div>
              <label for="code" class="block text-sm font-medium text-gray-700 mb-2">
                Código de Verificação
              </label>
              <input
                id="code"
                name="code"
                type="text"
                required
                maxlength="6"
                x-model="verificationCode"
                class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500 transition-colors text-center text-lg font-mono tracking-widest"
                placeholder="000000"
              >
            </div>

            <button
              type="submit"
              :disabled="loading || verificationCode.length !== 6"
              class="w-full flex justify-center py-3 px-4 border border-transparent rounded-lg shadow-sm text-sm font-medium text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 disabled:bg-gray-400 disabled:cursor-not-allowed transition-colors"
            >
              <svg x-show="loading" class="animate-spin -ml-1 mr-3 h-5 w-5 text-white" fill="none" viewBox="0 0 24 24">
                <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
              </svg>
              <span x-show="!loading">Verificar Código</span>
              <span x-show="loading">Verificando...</span>
            </button>

            <!-- Resend Code -->
            <div class="text-center">
              <p class="text-sm text-gray-600">
                Não recebeu o código?
                <button
                  type="button"
                  @click="resendCode()"
                  :disabled="resendCooldown > 0"
                  class="font-medium text-primary-600 hover:text-primary-500 disabled:text-gray-400 disabled:cursor-not-allowed"
                >
                  <span x-show="resendCooldown === 0">Reenviar</span>
                  <span x-show="resendCooldown > 0">Reenviar em <span x-text="resendCooldown"></span>s</span>
                </button>
              </p>
            </div>
          </form>
        </div>

        <!-- Step 3: New Password -->
        <div x-show="step === 3" x-transition>
          <div class="text-center mb-6">
            <div class="mx-auto h-12 w-12 bg-green-100 rounded-full flex items-center justify-center mb-4">
              <svg class="h-6 w-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
              </svg>
            </div>
            <h3 class="text-lg font-medium text-gray-900">Código Verificado!</h3>
            <p class="text-sm text-gray-600 mt-1">
              Agora defina sua nova senha
            </p>
          </div>

          <!-- Alert Messages -->
          <div x-show="alert.show" x-transition class="mb-4 p-4 rounded-lg" :class="alert.type === 'success' ? 'bg-green-100 text-green-700' : 'bg-red-100 text-red-700'">
            <p x-text="alert.message"></p>
          </div>

          <form @submit.prevent="resetPassword()" class="space-y-6">
            <!-- Nova Senha -->
            <div>
              <label for="newPassword" class="block text-sm font-medium text-gray-700 mb-2">
                Nova Senha
              </label>
              <div class="relative">
                <input
                  id="newPassword"
                  name="newPassword"
                  :type="showPassword ? 'text' : 'password'"
                  required
                  x-model="newPassword"
                  class="w-full px-4 py-3 pr-12 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500 transition-colors"
                  placeholder="Digite sua nova senha"
                >
                <button
                  type="button"
                  @click="showPassword = !showPassword"
                  class="absolute inset-y-0 right-0 pr-3 flex items-center"
                >
                  <svg x-show="!showPassword" class="h-5 w-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
                  </svg>
                  <svg x-show="showPassword" class="h-5 w-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13.875 18.825A10.05 10.05 0 0112 19c-4.478 0-8.268-2.943-9.543-7a9.97 9.97 0 011.563-3.029m5.858.908a3 3 0 114.243 4.243M9.878 9.878l4.242 4.242M9.878 9.878L3 3m6.878 6.878L21 21"></path>
                  </svg>
                </button>
              </div>
              <!-- Password Strength Indicator -->
              <div class="mt-2">
                <div class="flex space-x-1">
                  <div class="h-1 flex-1 rounded" :class="passwordStrength >= 1 ? 'bg-red-500' : 'bg-gray-200'"></div>
                  <div class="h-1 flex-1 rounded" :class="passwordStrength >= 2 ? 'bg-yellow-500' : 'bg-gray-200'"></div>
                  <div class="h-1 flex-1 rounded" :class="passwordStrength >= 3 ? 'bg-green-500' : 'bg-gray-200'"></div>
                </div>
                <p class="text-xs text-gray-500 mt-1" x-text="passwordStrengthText"></p>
              </div>
            </div>

            <!-- Confirmar Nova Senha -->
            <div>
              <label for="confirmNewPassword" class="block text-sm font-medium text-gray-700 mb-2">
                Confirmar Nova Senha
              </label>
              <input
                id="confirmNewPassword"
                name="confirmNewPassword"
                type="password"
                required
                x-model="confirmNewPassword"
                class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500 transition-colors"
                placeholder="Confirme sua nova senha"
              >
              <p x-show="confirmNewPassword && newPassword !== confirmNewPassword" class="text-xs text-red-500 mt-1">
                As senhas não coincidem
              </p>
            </div>

            <button
              type="submit"
              :disabled="loading || !isPasswordValid"
              class="w-full flex justify-center py-3 px-4 border border-transparent rounded-lg shadow-sm text-sm font-medium text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 disabled:bg-gray-400 disabled:cursor-not-allowed transition-colors"
            >
              <svg x-show="loading" class="animate-spin -ml-1 mr-3 h-5 w-5 text-white" fill="none" viewBox="0 0 24 24">
                <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
              </svg>
              <span x-show="!loading">Redefinir Senha</span>
              <span x-show="loading">Redefinindo...</span>
            </button>
          </form>
        </div>

        <!-- Back to Login -->
        <div class="mt-6 text-center">
          <a href="/login" class="text-sm font-medium text-primary-600 hover:text-primary-500 transition-colors">
            ← Voltar para o login
          </a>
        </div>
      </div>
    </div>
  </div>

  <script>
    function esqueceuSenhaData() {
      return {
        step: 1,
        loading: false,
        showPassword: false,
        email: '',
        verificationCode: '',
        newPassword: '',
        confirmNewPassword: '',
        resendCooldown: 0,
        alert: {
          show: false,
          type: 'success',
          message: ''
        },

        get passwordStrength() {
          const password = this.newPassword;
          if (!password) return 0;
          
          let strength = 0;
          if (password.length >= 8) strength++;
          if (/[A-Z]/.test(password) && /[a-z]/.test(password)) strength++;
          if (/\d/.test(password) && /[!@#$%^&*(),.?":{}|<>]/.test(password)) strength++;
          
          return strength;
        },

        get passwordStrengthText() {
          switch (this.passwordStrength) {
            case 0: return 'Muito fraca';
            case 1: return 'Fraca';
            case 2: return 'Média';
            case 3: return 'Forte';
            default: return '';
          }
        },

        get isPasswordValid() {
          return this.newPassword && 
                 this.confirmNewPassword &&
                 this.newPassword === this.confirmNewPassword &&
                 this.passwordStrength >= 2;
        },

        showAlert(type, message) {
          this.alert = { show: true, type, message };
          setTimeout(() => {
            this.alert.show = false;
          }, 5000);
        },

        startResendCooldown() {
          this.resendCooldown = 60;
          const interval = setInterval(() => {
            this.resendCooldown--;
            if (this.resendCooldown <= 0) {
              clearInterval(interval);
            }
          }, 1000);
        },

        async sendResetEmail() {
          this.loading = true;

          try {
            const response = await fetch('/api/auth/forgot-password', {
              method: 'POST',
              headers: {
                'Content-Type': 'application/json'
              },
              body: JSON.stringify({ email: this.email })
            });

            const data = await response.json();

            if (data.success) {
              this.step = 2;
              this.startResendCooldown();
              this.showAlert('success', 'Código enviado com sucesso!');
            } else {
              this.showAlert('error', data.message || 'Erro ao enviar código. Tente novamente.');
            }
          } catch (error) {
            console.error('Erro ao enviar email:', error);
            this.showAlert('error', 'Erro de conexão. Tente novamente.');
          } finally {
            this.loading = false;
          }
        },

        async verifyCode() {
          this.loading = true;

          try {
            const response = await fetch('/api/auth/verify-reset-code', {
              method: 'POST',
              headers: {
                'Content-Type': 'application/json'
              },
              body: JSON.stringify({ 
                email: this.email,
                code: this.verificationCode 
              })
            });

            const data = await response.json();

            if (data.success) {
              this.step = 3;
              this.showAlert('success', 'Código verificado com sucesso!');
            } else {
              this.showAlert('error', data.message || 'Código inválido. Tente novamente.');
            }
          } catch (error) {
            console.error('Erro ao verificar código:', error);
            this.showAlert('error', 'Erro de conexão. Tente novamente.');
          } finally {
            this.loading = false;
          }
        },

        async resetPassword() {
          this.loading = true;

          try {
            const response = await fetch('/api/auth/reset-password', {
              method: 'POST',
              headers: {
                'Content-Type': 'application/json'
              },
              body: JSON.stringify({ 
                email: this.email,
                code: this.verificationCode,
                newPassword: this.newPassword
              })
            });

            const data = await response.json();

            if (data.success) {
              this.showAlert('success', 'Senha redefinida com sucesso! Redirecionando...');
              setTimeout(() => {
                window.location.href = '/login?reset=true';
              }, 2000);
            } else {
              this.showAlert('error', data.message || 'Erro ao redefinir senha. Tente novamente.');
            }
          } catch (error) {
            console.error('Erro ao redefinir senha:', error);
            this.showAlert('error', 'Erro de conexão. Tente novamente.');
          } finally {
            this.loading = false;
          }
        },

        async resendCode() {
          await this.sendResetEmail();
        }
      }
    }
  </script>
</body>
</html>
