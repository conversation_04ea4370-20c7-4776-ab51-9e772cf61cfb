# Sistema de Reservas - Frontend

Este documento descreve a implementação completa do frontend do Sistema de Reservas, desenvolvido com EJS, Tailwind CSS e Alpine.js.

## 🎨 Design System

### Cores Principais
- **Primary Blue**: #3b82f6 (Azul principal para botões e elementos de destaque)
- **Primary Dark**: #1d4ed8 (Variação mais escura do azul)
- **Gray Scale**: #f9fafb, #f3f4f6, #e5e7eb, #d1d5db (Tons de cinza para backgrounds e bordas)
- **Success**: #10b981 (Verde para status de sucesso)
- **Warning**: #f59e0b (Amarelo para avisos)
- **Error**: #ef4444 (Vermelho para erros)

### Tipografia
- **Font Family**: Inter, system-ui, sans-serif
- **Font Sizes**: Utilizando escala do Tailwind (text-sm, text-base, text-lg, etc.)
- **Font Weights**: 400 (normal), 500 (medium), 600 (semibold), 700 (bold)

## 📱 Páginas Implementadas

### 1. <PERSON><PERSON> (`/login`)
- **Arquivo**: `views/pages/login.ejs`
- **Funcionalidades**:
  - Formulário de autenticação
  - Validação de campos
  - Toggle de visibilidade da senha
  - Opção "Lembrar de mim"
  - Link para recuperação de senha
  - Design responsivo

### 2. Dashboard (`/dashboard`)
- **Arquivo**: `views/pages/dashboard.ejs`
- **Funcionalidades**:
  - Cards de estatísticas (Total de Reservas, Reservas Ativas, etc.)
  - Ações rápidas (Nova Reserva, Buscar Salas, Ver Calendário)
  - Lista de próximas reservas
  - Salas mais utilizadas
  - Layout responsivo com sidebar

### 3. Minhas Reservas (`/minhas-reservas`)
- **Arquivo**: `views/pages/minhas-reservas.ejs`
- **Funcionalidades**:
  - Lista completa de reservas do usuário
  - Filtros por status e período
  - Busca por título ou sala
  - Ações por reserva (Ver, Editar, Cancelar)
  - Status badges coloridos
  - Paginação (preparado para implementação)

### 4. Nova Reserva (`/nova-reserva`)
- **Arquivo**: `views/pages/nova-reserva.ejs`
- **Funcionalidades**:
  - Formulário completo para criação de reservas
  - Seleção de sala com informações detalhadas
  - Configuração de data e horário
  - Opções de recorrência
  - Validação de formulário
  - Preview da sala selecionada

## 🧩 Componentes Reutilizáveis

### 1. Dashboard Header (`views/components/dashboard-header.ejs`)
- Logo e título do sistema
- Barra de busca global
- Notificações com dropdown
- Menu do usuário com opções

### 2. Sidebar (`views/components/sidebar.ejs`)
- Navegação principal
- Menu administrativo (condicional)
- Resumo rápido de estatísticas
- Seção de ajuda

### 3. Modal (`views/components/modal.ejs`)
- Modal genérico reutilizável
- Variantes: confirmação, sucesso, erro
- Animações de entrada e saída
- Backdrop com blur

## 🎯 Tecnologias Utilizadas

### Tailwind CSS
- **CDN**: https://cdn.tailwindcss.com
- **Configuração**: Cores customizadas no tema
- **Utilitários**: Classes responsivas, hover states, transitions

### Alpine.js
- **CDN**: https://unpkg.com/alpinejs@3.x.x/dist/cdn.min.js
- **Funcionalidades**:
  - Reatividade de dados
  - Manipulação de estado
  - Eventos e interações
  - Transições e animações

### EJS (Embedded JavaScript)
- **Template Engine**: Para renderização server-side
- **Partials**: Componentes reutilizáveis
- **Layouts**: Estrutura base compartilhada

## 📁 Estrutura de Arquivos

```
views/
├── layout/
│   └── main.ejs              # Layout principal
├── pages/
│   ├── login.ejs             # Página de login
│   ├── dashboard.ejs         # Dashboard principal
│   ├── minhas-reservas.ejs   # Lista de reservas
│   └── nova-reserva.ejs      # Formulário de nova reserva
├── components/
│   ├── dashboard-header.ejs  # Cabeçalho do dashboard
│   ├── sidebar.ejs           # Barra lateral de navegação
│   └── modal.ejs             # Componente de modal
└── css/
    └── style.css             # Estilos customizados
```

## 🚀 Funcionalidades Implementadas

### Autenticação
- [x] Página de login responsiva
- [x] Validação de formulário
- [x] Estados de loading
- [x] Tratamento de erros

### Dashboard
- [x] Cards de estatísticas
- [x] Ações rápidas
- [x] Lista de próximas reservas
- [x] Salas populares

### Gerenciamento de Reservas
- [x] Listagem com filtros
- [x] Busca por texto
- [x] Status badges
- [x] Ações por item (Ver, Editar, Cancelar)
- [x] Formulário de criação
- [x] Validação de dados
- [x] Opções de recorrência

### Interface
- [x] Design responsivo
- [x] Navegação intuitiva
- [x] Feedback visual
- [x] Animações suaves
- [x] Estados de loading
- [x] Tratamento de erros

## 🎨 Padrões de Design

### Cores de Status
- **Confirmada**: Verde (#10b981)
- **Pendente**: Amarelo (#f59e0b)
- **Cancelada**: Vermelho (#ef4444)

### Espaçamentos
- **Padding**: 4, 6, 8 (1rem, 1.5rem, 2rem)
- **Margins**: 4, 6, 8 (1rem, 1.5rem, 2rem)
- **Gaps**: 4, 6, 8 (1rem, 1.5rem, 2rem)

### Bordas e Sombras
- **Border Radius**: rounded-lg (0.5rem)
- **Shadows**: shadow-sm, shadow-lg
- **Borders**: border-gray-200, border-gray-300

## 📱 Responsividade

### Breakpoints
- **Mobile**: < 768px
- **Tablet**: 768px - 1024px
- **Desktop**: > 1024px

### Adaptações
- Sidebar colapsível em mobile
- Cards empilhados em telas pequenas
- Formulários adaptáveis
- Tabelas com scroll horizontal

## 🔧 Configuração e Uso

### Instalação
```bash
npm install
```

### Desenvolvimento
```bash
npm run dev
```

### Produção
```bash
npm start
```

### Rotas Disponíveis
- `/` - Redireciona para login
- `/login` - Página de autenticação
- `/dashboard` - Dashboard principal
- `/minhas-reservas` - Lista de reservas
- `/nova-reserva` - Criar nova reserva

## 🎯 Próximos Passos

### Funcionalidades Pendentes
- [ ] Integração com API backend
- [ ] Autenticação real com JWT
- [ ] Upload de imagens
- [ ] Notificações em tempo real
- [ ] Calendário interativo
- [ ] Relatórios e analytics
- [ ] Configurações de usuário
- [ ] Modo escuro

### Melhorias de UX
- [ ] Skeleton loading
- [ ] Infinite scroll
- [ ] Drag and drop
- [ ] Atalhos de teclado
- [ ] Tour guiado
- [ ] Feedback háptico (mobile)

## 📝 Notas de Desenvolvimento

### Convenções
- Nomes de arquivos em kebab-case
- Classes CSS seguindo padrões do Tailwind
- Funções Alpine.js em camelCase
- Comentários em português

### Performance
- CSS e JS carregados via CDN
- Imagens otimizadas
- Lazy loading implementado
- Minificação em produção

### Acessibilidade
- Contraste adequado
- Navegação por teclado
- Labels descritivos
- ARIA attributes
- Foco visível

Este frontend está pronto para integração com o backend existente e pode ser facilmente expandido com novas funcionalidades.
