<header class="bg-white shadow-sm border-b border-gray-200" x-data="headerData()">
  <div class="px-6 py-4">
    <div class="flex items-center justify-between">
      <div class="flex items-center space-x-4">
        <div class="h-8 w-8 bg-primary-600 rounded-lg flex items-center justify-center">
          <svg class="h-5 w-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-2m-2 0H7m5 0v-5a2 2 0 00-2-2H8a2 2 0 00-2 2v5m5 0V9a2 2 0 012-2h2a2 2 0 012 2v12"></path>
          </svg>
        </div>
        <h1 class="text-xl font-semibold text-gray-900">Sistema de Reservas</h1>
      </div>

      <div class="flex-1 max-w-lg mx-8">
        <div class="relative">
          <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
            <svg class="h-5 w-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
            </svg>
          </div>
          <input 
            type="text" 
            placeholder="Buscar salas, reservas..." 
            class="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-lg leading-5 bg-white placeholder-gray-500 focus:outline-none focus:placeholder-gray-400 focus:ring-1 focus:ring-primary-500 focus:border-primary-500"
            x-model="searchQuery"
          >
        </div>
      </div>

      <div class="flex items-center space-x-4">
        <div class="relative" x-data="{ open: false }">
          <button 
            @click="open = !open"
            class="p-2 text-gray-400 hover:text-gray-500 relative"
          >
            <svg class="h-6 w-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 17h5l-3.5-3.5a50.002 50.002 0 00-2.5-2.5M15 17v2a3 3 0 11-6 0v-2m6 0H9m6 0a3 3 0 01-3 3 3 3 0 01-3-3m3-3V9a3 3 0 116 0v6.5z"></path>
            </svg>
            <span class="absolute top-0 right-0 block h-2 w-2 rounded-full bg-red-400 ring-2 ring-white"></span>
          </button>

          <div 
            x-show="open" 
            @click.away="open = false"
            x-transition:enter="transition ease-out duration-100"
            x-transition:enter-start="transform opacity-0 scale-95"
            x-transition:enter-end="transform opacity-100 scale-100"
            x-transition:leave="transition ease-in duration-75"
            x-transition:leave-start="transform opacity-100 scale-100"
            x-transition:leave-end="transform opacity-0 scale-95"
            class="absolute right-0 mt-2 w-80 bg-white rounded-lg shadow-lg ring-1 ring-black ring-opacity-5 z-50"
          >
            <div class="p-4 border-b border-gray-200">
              <h3 class="text-sm font-medium text-gray-900">Notificações</h3>
            </div>
            <div class="max-h-64 overflow-y-auto">
              <template x-for="notification in notifications" :key="notification.id">
                <div class="p-4 border-b border-gray-100 hover:bg-gray-50">
                  <div class="flex items-start space-x-3">
                    <div class="flex-shrink-0">
                      <div class="h-8 w-8 bg-blue-100 rounded-full flex items-center justify-center">
                        <svg class="h-4 w-4 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                        </svg>
                      </div>
                    </div>
                    <div class="flex-1 min-w-0">
                      <p class="text-sm font-medium text-gray-900" x-text="notification.title"></p>
                      <p class="text-sm text-gray-500" x-text="notification.message"></p>
                      <p class="text-xs text-gray-400 mt-1" x-text="notification.time"></p>
                    </div>
                  </div>
                </div>
              </template>
            </div>
            <div class="p-4">
              <a href="#" class="text-sm text-primary-600 hover:text-primary-500 font-medium">Ver todas as notificações</a>
            </div>
          </div>
        </div>

        <div class="relative" x-data="{ open: false }">
          <button 
            @click="open = !open"
            class="flex items-center space-x-3 p-2 rounded-lg hover:bg-gray-100"
          >
            <div class="h-8 w-8 bg-gray-300 rounded-full flex items-center justify-center">
              <span class="text-sm font-medium text-gray-700">
                <% if (locals.currentUser) { %>
                  <%= currentUser.nome.charAt(0).toUpperCase() %>
                <% } else { %>
                  U
                <% } %>
              </span>
            </div>
            <div class="hidden md:block text-left">
              <p class="text-sm font-medium text-gray-900">
                <% if (locals.currentUser) { %>
                  <%= currentUser.nome %>
                <% } else { %>
                  Usuário
                <% } %>
              </p>
              <p class="text-xs text-gray-500">
                <% if (locals.currentUser && locals.currentUser.departamento) { %>
                  <%= currentUser.departamento %>
                <% } else { %>
                  Usuário
                <% } %>
              </p>
            </div>
            <svg class="h-4 w-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
            </svg>
          </button>

          <div 
            x-show="open" 
            @click.away="open = false"
            x-transition:enter="transition ease-out duration-100"
            x-transition:enter-start="transform opacity-0 scale-95"
            x-transition:enter-end="transform opacity-100 scale-100"
            x-transition:leave="transition ease-in duration-75"
            x-transition:leave-start="transform opacity-100 scale-100"
            x-transition:leave-end="transform opacity-0 scale-95"
            class="absolute right-0 mt-2 w-48 bg-white rounded-lg shadow-lg ring-1 ring-black ring-opacity-5 z-50"
          >
            <div class="py-1">
              <a href="/perfil" class="flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                <svg class="mr-3 h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                </svg>
                Meu Perfil
              </a>
              <a href="/configuracoes" class="flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                <svg class="mr-3 h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"></path>
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                </svg>
                Configurações
              </a>
              <div class="border-t border-gray-100"></div>
              <button
                @click="logout()"
                class="flex items-center w-full px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 text-left"
              >
                <svg class="mr-3 h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1"></path>
                </svg>
                Sair
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</header>

<script>
  function headerData() {
    return {
      searchQuery: '',
      notifications: [
        {
          id: 1,
          title: 'Reserva Confirmada',
          message: 'Sua reserva para a Sala A foi confirmada',
          time: '5 min atrás'
        },
        {
          id: 2,
          title: 'Lembrete',
          message: 'Reunião em 30 minutos no Auditório',
          time: '25 min atrás'
        },
        {
          id: 3,
          title: 'Cancelamento',
          message: 'Reserva da Sala B foi cancelada',
          time: '1 hora atrás'
        }
      ],

      async logout() {
        try {
          const response = await fetch('/api/auth/logout', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json'
            }
          });

          const data = await response.json();

          if (data.success) {
            window.location.href = data.redirectUrl || '/login';
          } else {
            alert('Erro ao fazer logout');
          }
        } catch (error) {
          console.error('Erro no logout:', error);
          alert('Erro de conexão');
        }
      }
    }
  }
</script>
