/* Siste<PERSON> de <PERSON> - Light/Dark Mode */

/* Variáveis CSS para Tema Claro */
:root,
html[data-theme="light"] {
  --bg-primary: #ffffff;
  --bg-secondary: #f8fafc;
  --bg-tertiary: #f1f5f9;
  
  --text-primary: #1f2937;
  --text-secondary: #6b7280;
  --text-tertiary: #9ca3af;
  
  --border-primary: #e5e7eb;
  --border-secondary: #d1d5db;
  
  --primary-50: #eff6ff;
  --primary-100: #dbeafe;
  --primary-500: #3b82f6;
  --primary-600: #2563eb;
  --primary-700: #1d4ed8;
  
  --success-100: #dcfce7;
  --success-800: #166534;
  
  --warning-100: #fef3c7;
  --warning-800: #92400e;
  
  --error-100: #fee2e2;
  --error-800: #991b1b;
  
  --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
  --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
  --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
}

/* Variáveis CSS para Tema Escuro */
html[data-theme="dark"] {
  --bg-primary: #111827;
  --bg-secondary: #1f2937;
  --bg-tertiary: #374151;
  
  --text-primary: #f9fafb;
  --text-secondary: #d1d5db;
  --text-tertiary: #9ca3af;
  
  --border-primary: #374151;
  --border-secondary: #4b5563;
  
  --primary-50: #1e3a8a;
  --primary-100: #1e40af;
  --primary-500: #3b82f6;
  --primary-600: #60a5fa;
  --primary-700: #93c5fd;
  
  --success-100: #064e3b;
  --success-800: #10b981;
  
  --warning-100: #78350f;
  --warning-800: #f59e0b;
  
  --error-100: #7f1d1d;
  --error-800: #ef4444;
  
  --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.3);
  --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.3), 0 2px 4px -2px rgb(0 0 0 / 0.3);
  --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.3), 0 4px 6px -4px rgb(0 0 0 / 0.3);
}

/* Classes Utilitárias para Temas */
.theme-bg-primary {
  background-color: var(--bg-primary);
}

.theme-bg-secondary {
  background-color: var(--bg-secondary);
}

.theme-bg-tertiary {
  background-color: var(--bg-tertiary);
}

.theme-text-primary {
  color: var(--text-primary);
}

.theme-text-secondary {
  color: var(--text-secondary);
}

.theme-text-tertiary {
  color: var(--text-tertiary);
}

.theme-border-primary {
  border-color: var(--border-primary);
}

.theme-border-secondary {
  border-color: var(--border-secondary);
}

.theme-shadow-sm {
  box-shadow: var(--shadow-sm);
}

.theme-shadow-md {
  box-shadow: var(--shadow-md);
}

.theme-shadow-lg {
  box-shadow: var(--shadow-lg);
}

/* Aplicação Automática de Temas */
body {
  background-color: var(--bg-secondary);
  color: var(--text-primary);
  transition: background-color 0.3s ease, color 0.3s ease;
}

/* Cards e Containers */
.bg-white {
  background-color: var(--bg-primary) !important;
}

.bg-gray-50 {
  background-color: var(--bg-secondary) !important;
}

.bg-gray-100 {
  background-color: var(--bg-tertiary) !important;
}

/* Texto */
.text-gray-900 {
  color: var(--text-primary) !important;
}

.text-gray-700 {
  color: var(--text-primary) !important;
}

.text-gray-600 {
  color: var(--text-secondary) !important;
}

.text-gray-500 {
  color: var(--text-tertiary) !important;
}

/* Bordas */
.border-gray-200 {
  border-color: var(--border-primary) !important;
}

.border-gray-300 {
  border-color: var(--border-secondary) !important;
}

/* Sombras */
.shadow-sm {
  box-shadow: var(--shadow-sm) !important;
}

.shadow-md {
  box-shadow: var(--shadow-md) !important;
}

.shadow-lg {
  box-shadow: var(--shadow-lg) !important;
}

/* Hover States */
.hover\:bg-gray-50:hover {
  background-color: var(--bg-tertiary) !important;
}

.hover\:bg-gray-100:hover {
  background-color: var(--border-primary) !important;
}

/* Focus States */
.focus\:ring-primary-500:focus {
  --tw-ring-color: var(--primary-500);
}

.focus\:border-primary-500:focus {
  border-color: var(--primary-500);
}

/* Status Colors */
.bg-green-100 {
  background-color: var(--success-100) !important;
}

.text-green-800 {
  color: var(--success-800) !important;
}

.bg-yellow-100 {
  background-color: var(--warning-100) !important;
}

.text-yellow-800 {
  color: var(--warning-800) !important;
}

.bg-red-100 {
  background-color: var(--error-100) !important;
}

.text-red-800 {
  color: var(--error-800) !important;
}

/* Transições Suaves */
* {
  transition: background-color 0.3s ease, color 0.3s ease, border-color 0.3s ease;
}

/* Scrollbar para Tema Escuro */
html[data-theme="dark"] ::-webkit-scrollbar {
  width: 8px;
}

html[data-theme="dark"] ::-webkit-scrollbar-track {
  background: var(--bg-secondary);
}

html[data-theme="dark"] ::-webkit-scrollbar-thumb {
  background: var(--border-secondary);
  border-radius: 4px;
}

html[data-theme="dark"] ::-webkit-scrollbar-thumb:hover {
  background: var(--text-tertiary);
}

/* Inputs no Tema Escuro */
html[data-theme="dark"] input,
html[data-theme="dark"] select,
html[data-theme="dark"] textarea {
  background-color: var(--bg-tertiary);
  border-color: var(--border-secondary);
  color: var(--text-primary);
}

html[data-theme="dark"] input:focus,
html[data-theme="dark"] select:focus,
html[data-theme="dark"] textarea:focus {
  background-color: var(--bg-primary);
  border-color: var(--primary-500);
}

/* Placeholder no Tema Escuro */
html[data-theme="dark"] input::placeholder,
html[data-theme="dark"] textarea::placeholder {
  color: var(--text-tertiary);
}

/* Modais no Tema Escuro */
html[data-theme="dark"] .fixed.inset-0.bg-gray-600 {
  background-color: rgba(0, 0, 0, 0.7) !important;
}
