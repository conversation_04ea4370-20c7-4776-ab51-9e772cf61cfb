// Theme Manager - Sistema Global de Temas e Configurações
class ThemeManager {
  constructor() {
    this.settings = this.loadSettings();
    this.init();
  }

  // Configurações padrão
  getDefaultSettings() {
    return {
      // Notificações
      emailNotifications: true,
      meetingReminders: true,
      cancellationNotifications: true,
      
      // Exibição
      theme: 'light',
      language: 'pt-BR',
      timezone: 'America/Sao_Paulo',
      
      // Calendário
      defaultCalendarView: 'month',
      weekStartDay: '1',
      showWeekends: true
    };
  }

  // Carregar configurações do localStorage
  loadSettings() {
    try {
      const saved = localStorage.getItem('userSettings');
      if (saved) {
        return { ...this.getDefaultSettings(), ...JSON.parse(saved) };
      }
    } catch (error) {
      console.error('Erro ao carregar configurações:', error);
    }
    return this.getDefaultSettings();
  }

  // Salvar configurações no localStorage
  saveSettings(newSettings = null) {
    try {
      const settingsToSave = newSettings || this.settings;
      localStorage.setItem('userSettings', JSON.stringify(settingsToSave));
      
      // Sincronizar com servidor
      this.syncWithServer(settingsToSave);
      
      // Aplicar mudanças imediatamente
      this.applySettings(settingsToSave);
      
      return true;
    } catch (error) {
      console.error('Erro ao salvar configurações:', error);
      return false;
    }
  }

  // Aplicar configurações
  applySettings(settings = null) {
    const currentSettings = settings || this.settings;
    
    // Aplicar tema
    this.applyTheme(currentSettings.theme);
    
    // Aplicar idioma
    this.applyLanguage(currentSettings.language);
    
    // Disparar evento para outras páginas
    this.broadcastSettingsChange(currentSettings);
  }

  // Aplicar tema
  applyTheme(theme) {
    const html = document.documentElement;
    
    // Remover classes de tema existentes
    html.classList.remove('light', 'dark');
    
    if (theme === 'auto') {
      // Detectar preferência do sistema
      const prefersDark = window.matchMedia('(prefers-color-scheme: dark)').matches;
      theme = prefersDark ? 'dark' : 'light';
    }
    
    // Aplicar tema
    html.classList.add(theme);
    html.setAttribute('data-theme', theme);
    
    // Salvar tema aplicado
    this.settings.theme = theme;
    
    console.log(`Tema aplicado: ${theme}`);
  }

  // Aplicar idioma
  applyLanguage(language) {
    document.documentElement.lang = language.split('-')[0];
    this.settings.language = language;
  }

  // Sincronizar com servidor
  async syncWithServer(settings) {
    try {
      const response = await fetch('/api/users/settings', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(settings)
      });
      
      if (!response.ok) {
        console.warn('Falha ao sincronizar configurações com servidor');
      }
    } catch (error) {
      console.warn('Erro ao sincronizar com servidor:', error);
    }
  }

  // Carregar configurações do servidor
  async loadFromServer() {
    try {
      const response = await fetch('/api/users/settings');
      if (response.ok) {
        const data = await response.json();
        if (data.success && data.data) {
          this.settings = { ...this.getDefaultSettings(), ...data.data };
          this.saveSettings();
          return true;
        }
      }
    } catch (error) {
      console.warn('Erro ao carregar configurações do servidor:', error);
    }
    return false;
  }

  // Broadcast para outras abas/páginas
  broadcastSettingsChange(settings) {
    // Storage event para outras abas
    window.dispatchEvent(new CustomEvent('settingsChanged', { 
      detail: settings 
    }));
    
    // Broadcast channel para comunicação entre abas
    if (window.BroadcastChannel) {
      const channel = new BroadcastChannel('userSettings');
      channel.postMessage({ type: 'settingsUpdate', settings });
    }
  }

  // Atualizar configuração específica
  updateSetting(key, value) {
    this.settings[key] = value;
    this.saveSettings();
  }

  // Obter configuração específica
  getSetting(key) {
    return this.settings[key];
  }

  // Obter todas as configurações
  getAllSettings() {
    return { ...this.settings };
  }

  // Resetar para padrões
  resetToDefaults() {
    this.settings = this.getDefaultSettings();
    this.saveSettings();
  }

  // Inicializar
  init() {
    // Aplicar configurações iniciais
    this.applySettings();
    
    // Escutar mudanças de tema do sistema
    if (window.matchMedia) {
      window.matchMedia('(prefers-color-scheme: dark)').addEventListener('change', (e) => {
        if (this.settings.theme === 'auto') {
          this.applyTheme('auto');
        }
      });
    }
    
    // Escutar mudanças de outras abas
    window.addEventListener('storage', (e) => {
      if (e.key === 'userSettings') {
        this.settings = this.loadSettings();
        this.applySettings();
      }
    });
    
    // Escutar broadcast de outras abas
    if (window.BroadcastChannel) {
      const channel = new BroadcastChannel('userSettings');
      channel.addEventListener('message', (e) => {
        if (e.data.type === 'settingsUpdate') {
          this.settings = e.data.settings;
          this.applySettings();
        }
      });
    }
    
    // Carregar do servidor se disponível
    this.loadFromServer();
  }
}

// Instância global
window.themeManager = new ThemeManager();

// Função helper para componentes Alpine.js
window.getThemeManager = () => window.themeManager;
