// Theme Manager - Sistema Global de Temas e Configurações
class ThemeManager {
  constructor() {
    this.settings = this.loadSettings();
    this.init();
  }

  // Configurações padrão
  getDefaultSettings() {
    return {
      // Notificações
      emailNotifications: true,
      meetingReminders: true,
      cancellationNotifications: true,
      
      // Exibição
      theme: 'light',
      language: 'pt-BR',
      timezone: 'America/Sao_Paulo',
      
      // Calendário
      defaultCalendarView: 'month',
      weekStartDay: '1',
      showWeekends: true
    };
  }

  // Carregar configurações do localStorage
  loadSettings() {
    try {
      const saved = localStorage.getItem('userSettings');
      if (saved) {
        return { ...this.getDefaultSettings(), ...JSON.parse(saved) };
      }
    } catch (error) {
      console.error('Erro ao carregar configurações:', error);
    }
    return this.getDefaultSettings();
  }

  // Salvar configurações no localStorage
  saveSettings(newSettings = null) {
    try {
      const settingsToSave = newSettings || this.settings;

      // Atualizar configurações internas
      this.settings = { ...this.settings, ...settingsToSave };

      // Salvar no localStorage
      localStorage.setItem('userSettings', JSON.stringify(this.settings));

      // Sincronizar com servidor (sem aplicar novamente)
      this.syncWithServer(this.settings);

      return true;
    } catch (error) {
      console.error('Erro ao salvar configurações:', error);
      return false;
    }
  }



  // Aplicar tema
  applyTheme(theme) {
    const html = document.documentElement;

    // Evitar loop infinito - verificar se o tema já está aplicado
    const currentTheme = html.getAttribute('data-theme');

    let targetTheme = theme;
    if (theme === 'auto') {
      // Detectar preferência do sistema
      const prefersDark = window.matchMedia('(prefers-color-scheme: dark)').matches;
      targetTheme = prefersDark ? 'dark' : 'light';
    }

    // Só aplicar se for diferente do atual
    if (currentTheme !== targetTheme) {
      // Remover classes de tema existentes
      html.classList.remove('light', 'dark');

      // Aplicar novo tema
      html.classList.add(targetTheme);
      html.setAttribute('data-theme', targetTheme);

      console.log(`Tema aplicado: ${targetTheme}`);
    }
  }

  // Aplicar idioma
  applyLanguage(language) {
    document.documentElement.lang = language.split('-')[0];
    this.settings.language = language;
  }

  // Sincronizar com servidor
  async syncWithServer(settings) {
    try {
      const response = await fetch('/api/users/settings', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(settings)
      });
      
      if (!response.ok) {
        console.warn('Falha ao sincronizar configurações com servidor');
      }
    } catch (error) {
      console.warn('Erro ao sincronizar com servidor:', error);
    }
  }

  // Carregar configurações do servidor
  async loadFromServer() {
    try {
      const response = await fetch('/api/users/settings');
      if (response.ok) {
        const data = await response.json();
        if (data.success && data.data) {
          const serverSettings = { ...this.getDefaultSettings(), ...data.data };

          // Só atualizar se for diferente
          if (JSON.stringify(serverSettings) !== JSON.stringify(this.settings)) {
            this.settings = serverSettings;
            localStorage.setItem('userSettings', JSON.stringify(this.settings));
            this.applyTheme(this.settings.theme);
            this.applyLanguage(this.settings.language);
          }
          return true;
        }
      }
    } catch (error) {
      console.warn('Erro ao carregar configurações do servidor:', error);
    }
    return false;
  }

  // Broadcast para outras abas/páginas
  broadcastSettingsChange(settings) {
    // Storage event para outras abas
    window.dispatchEvent(new CustomEvent('settingsChanged', { 
      detail: settings 
    }));
    
    // Broadcast channel para comunicação entre abas
    if (window.BroadcastChannel) {
      const channel = new BroadcastChannel('userSettings');
      channel.postMessage({ type: 'settingsUpdate', settings });
    }
  }

  // Atualizar configuração específica
  updateSetting(key, value) {
    // Evitar atualizações desnecessárias
    if (this.settings[key] === value) {
      return;
    }

    this.settings[key] = value;

    // Aplicar mudança específica
    if (key === 'theme') {
      this.applyTheme(value);
    } else if (key === 'language') {
      this.applyLanguage(value);
    }

    // Salvar sem reaplicar tudo
    this.saveSettings();

    // Broadcast para outras abas
    this.broadcastSettingsChange(this.settings);
  }

  // Obter configuração específica
  getSetting(key) {
    return this.settings[key];
  }

  // Obter todas as configurações
  getAllSettings() {
    return { ...this.settings };
  }

  // Resetar para padrões
  resetToDefaults() {
    this.settings = this.getDefaultSettings();
    this.saveSettings();
  }

  // Inicializar
  init() {
    // Aplicar configurações iniciais apenas uma vez
    this.applyTheme(this.settings.theme);
    this.applyLanguage(this.settings.language);

    // Escutar mudanças de tema do sistema
    if (window.matchMedia) {
      window.matchMedia('(prefers-color-scheme: dark)').addEventListener('change', (e) => {
        if (this.settings.theme === 'auto') {
          this.applyTheme('auto');
        }
      });
    }

    // Escutar mudanças de outras abas (sem reaplicar)
    window.addEventListener('storage', (e) => {
      if (e.key === 'userSettings') {
        const newSettings = this.loadSettings();
        if (JSON.stringify(newSettings) !== JSON.stringify(this.settings)) {
          this.settings = newSettings;
          this.applyTheme(this.settings.theme);
          this.applyLanguage(this.settings.language);
        }
      }
    });

    // Escutar broadcast de outras abas
    if (window.BroadcastChannel) {
      const channel = new BroadcastChannel('userSettings');
      channel.addEventListener('message', (e) => {
        if (e.data.type === 'settingsUpdate') {
          this.settings = e.data.settings;
          this.applyTheme(this.settings.theme);
          this.applyLanguage(this.settings.language);
        }
      });
    }

    // Carregar do servidor se disponível (sem reaplicar)
    this.loadFromServer();
  }
}

// Instância global
window.themeManager = new ThemeManager();

// Função helper para componentes Alpine.js
window.getThemeManager = () => window.themeManager;
