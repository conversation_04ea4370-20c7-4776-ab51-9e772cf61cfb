<!-- Admin <PERSON> Page -->
<div class="min-h-screen bg-gray-50" x-data="adminEdificiosData()">
  <!-- Header -->
  <%- include('../components/dashboard-header') %>
  
  <!-- Main Content -->
  <div class="flex">
    <!-- Sidebar -->
    <%- include('../components/sidebar') %>
    
    <!-- Main Content Area -->
    <main class="flex-1 p-6">
      <!-- Page Header -->
      <div class="mb-8">
        <div class="flex items-center justify-between">
          <div>
            <h1 class="text-2xl font-bold text-gray-900 mb-2">Gerenciar Edifícios</h1>
            <p class="text-gray-600">Administre todos os edifícios da organização</p>
          </div>
          <button 
            @click="showCreateModal = true"
            class="bg-primary-600 hover:bg-primary-700 text-white px-4 py-2 rounded-lg flex items-center space-x-2 transition-colors"
          >
            <svg class="h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
            </svg>
            <span>Novo Edifício</span>
          </button>
        </div>
      </div>

      <!-- Stats Cards -->
      <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <div class="flex items-center">
            <div class="p-2 bg-blue-100 rounded-lg">
              <svg class="h-6 w-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-2m-2 0H7m5 0v-5a2 2 0 00-2-2H8a2 2 0 00-2 2v5m5 0V9a2 2 0 012-2h2a2 2 0 012 2v12"></path>
              </svg>
            </div>
            <div class="ml-4">
              <p class="text-sm font-medium text-gray-600">Total de Edifícios</p>
              <p class="text-2xl font-bold text-gray-900" x-text="stats.total"></p>
            </div>
          </div>
        </div>

        <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <div class="flex items-center">
            <div class="p-2 bg-green-100 rounded-lg">
              <svg class="h-6 w-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
              </svg>
            </div>
            <div class="ml-4">
              <p class="text-sm font-medium text-gray-600">Total de Salas</p>
              <p class="text-2xl font-bold text-gray-900" x-text="stats.totalSalas"></p>
            </div>
          </div>
        </div>

        <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <div class="flex items-center">
            <div class="p-2 bg-purple-100 rounded-lg">
              <svg class="h-6 w-6 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM9 3a2 2 0 11-4 0 2 2 0 014 0z"></path>
              </svg>
            </div>
            <div class="ml-4">
              <p class="text-sm font-medium text-gray-600">Capacidade Total</p>
              <p class="text-2xl font-bold text-gray-900" x-text="stats.capacidadeTotal + ' pessoas'"></p>
            </div>
          </div>
        </div>

        <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <div class="flex items-center">
            <div class="p-2 bg-yellow-100 rounded-lg">
              <svg class="h-6 w-6 text-yellow-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"></path>
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"></path>
              </svg>
            </div>
            <div class="ml-4">
              <p class="text-sm font-medium text-gray-600">Localizações</p>
              <p class="text-2xl font-bold text-gray-900" x-text="stats.localizacoes"></p>
            </div>
          </div>
        </div>
      </div>

      <!-- Filters -->
      <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-4 mb-6">
        <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div>
            <input 
              type="text" 
              x-model="filters.search"
              placeholder="Buscar edifícios..."
              class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
            >
          </div>
          <div>
            <select 
              x-model="filters.cidade"
              class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
            >
              <option value="">Todas as cidades</option>
              <template x-for="cidade in uniqueCidades" :key="cidade">
                <option :value="cidade" x-text="cidade"></option>
              </template>
            </select>
          </div>
          <div>
            <select 
              x-model="filters.status"
              class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
            >
              <option value="">Todos os status</option>
              <option value="true">Ativo</option>
              <option value="false">Inativo</option>
            </select>
          </div>
        </div>
      </div>

      <!-- Buildings Grid -->
      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        <template x-for="edificio in filteredEdificios" :key="edificio.id">
          <div class="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
            <!-- Building Image -->
            <div class="h-48 bg-gradient-to-br from-blue-500 to-purple-600 relative">
              <div class="absolute inset-0 bg-black bg-opacity-20"></div>
              <div class="absolute bottom-4 left-4 text-white">
                <h3 class="text-lg font-semibold" x-text="edificio.nome"></h3>
                <p class="text-sm opacity-90" x-text="edificio.cidade + ', ' + edificio.estado"></p>
              </div>
              
              <!-- Status Badge -->
              <div class="absolute top-4 right-4">
                <span 
                  class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium"
                  :class="edificio.esta_ativo ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'"
                  x-text="edificio.esta_ativo ? 'Ativo' : 'Inativo'"
                ></span>
              </div>
            </div>

            <!-- Building Details -->
            <div class="p-6">
              <div class="space-y-3 mb-4">
                <div class="flex justify-between text-sm">
                  <span class="text-gray-500">Endereço:</span>
                  <span class="font-medium text-right" x-text="edificio.endereco"></span>
                </div>
                <div class="flex justify-between text-sm">
                  <span class="text-gray-500">Salas:</span>
                  <span class="font-medium" x-text="edificio.total_salas + ' salas'"></span>
                </div>
                <div class="flex justify-between text-sm">
                  <span class="text-gray-500">Capacidade:</span>
                  <span class="font-medium" x-text="edificio.capacidade_total + ' pessoas'"></span>
                </div>
                <div class="flex justify-between text-sm">
                  <span class="text-gray-500">Andares:</span>
                  <span class="font-medium" x-text="edificio.andares + ' andares'"></span>
                </div>
              </div>

              <!-- Actions -->
              <div class="flex space-x-2">
                <button 
                  @click="editEdificio(edificio)"
                  class="flex-1 bg-blue-600 hover:bg-blue-700 text-white px-3 py-2 rounded-lg text-sm font-medium transition-colors"
                >
                  Editar
                </button>
                <button 
                  @click="viewSalas(edificio)"
                  class="px-3 py-2 border border-gray-300 text-gray-700 rounded-lg text-sm font-medium hover:bg-gray-50 transition-colors"
                >
                  Salas
                </button>
                <button 
                  @click="toggleStatus(edificio)"
                  class="px-3 py-2 border border-gray-300 text-gray-700 rounded-lg text-sm font-medium hover:bg-gray-50 transition-colors"
                  x-text="edificio.esta_ativo ? 'Desativar' : 'Ativar'"
                ></button>
              </div>
            </div>
          </div>
        </template>

        <!-- Empty State -->
        <div x-show="filteredEdificios.length === 0" class="col-span-full text-center py-12">
          <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-2m-2 0H7m5 0v-5a2 2 0 00-2-2H8a2 2 0 00-2 2v5m5 0V9a2 2 0 012-2h2a2 2 0 012 2v12"></path>
          </svg>
          <h3 class="mt-2 text-sm font-medium text-gray-900">Nenhum edifício encontrado</h3>
          <p class="mt-1 text-sm text-gray-500">Comece criando um novo edifício.</p>
        </div>
      </div>

      <!-- Create/Edit Modal -->
      <div 
        x-show="showCreateModal || showEditModal" 
        x-transition:enter="ease-out duration-300"
        x-transition:enter-start="opacity-0"
        x-transition:enter-end="opacity-100"
        x-transition:leave="ease-in duration-200"
        x-transition:leave-start="opacity-100"
        x-transition:leave-end="opacity-0"
        class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50"
      >
        <div class="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
          <div class="mt-3">
            <h3 class="text-lg font-medium text-gray-900 mb-4" x-text="showCreateModal ? 'Novo Edifício' : 'Editar Edifício'"></h3>
            
            <form @submit.prevent="showCreateModal ? createEdificio() : updateEdificio()" class="space-y-4">
              <div>
                <label class="block text-sm font-medium text-gray-700 mb-1">Nome do Edifício</label>
                <input 
                  type="text" 
                  x-model="edificioForm.nome"
                  required
                  class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
                >
              </div>

              <div>
                <label class="block text-sm font-medium text-gray-700 mb-1">Endereço</label>
                <input 
                  type="text" 
                  x-model="edificioForm.endereco"
                  required
                  class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
                >
              </div>

              <div class="grid grid-cols-2 gap-4">
                <div>
                  <label class="block text-sm font-medium text-gray-700 mb-1">Cidade</label>
                  <input 
                    type="text" 
                    x-model="edificioForm.cidade"
                    required
                    class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
                  >
                </div>

                <div>
                  <label class="block text-sm font-medium text-gray-700 mb-1">Estado</label>
                  <input 
                    type="text" 
                    x-model="edificioForm.estado"
                    required
                    maxlength="2"
                    class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
                  >
                </div>
              </div>

              <div>
                <label class="block text-sm font-medium text-gray-700 mb-1">CEP</label>
                <input 
                  type="text" 
                  x-model="edificioForm.cep"
                  class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
                >
              </div>

              <div>
                <label class="block text-sm font-medium text-gray-700 mb-1">Número de Andares</label>
                <input 
                  type="number" 
                  x-model="edificioForm.andares"
                  required
                  min="1"
                  class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
                >
              </div>

              <div>
                <label class="block text-sm font-medium text-gray-700 mb-1">Descrição</label>
                <textarea 
                  x-model="edificioForm.descricao"
                  rows="3"
                  class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
                ></textarea>
              </div>

              <div class="flex items-center">
                <input 
                  type="checkbox" 
                  x-model="edificioForm.esta_ativo"
                  class="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"
                >
                <label class="ml-2 block text-sm text-gray-900">Edifício ativo</label>
              </div>

              <div class="flex justify-end space-x-3 pt-4">
                <button 
                  type="button"
                  @click="closeModal()"
                  class="px-4 py-2 text-sm font-medium text-gray-700 bg-gray-100 hover:bg-gray-200 rounded-lg transition-colors"
                >
                  Cancelar
                </button>
                <button 
                  type="submit"
                  :disabled="loading"
                  class="px-4 py-2 text-sm font-medium text-white bg-primary-600 hover:bg-primary-700 disabled:bg-gray-400 rounded-lg transition-colors"
                >
                  <span x-show="!loading" x-text="showCreateModal ? 'Criar Edifício' : 'Salvar Alterações'"></span>
                  <span x-show="loading">Salvando...</span>
                </button>
              </div>
            </form>
          </div>
        </div>
      </div>
    </main>
  </div>
</div>

<script>
  function adminEdificiosData() {
    return {
      loading: false,
      showCreateModal: false,
      showEditModal: false,
      edificios: [],
      filters: {
        search: '',
        cidade: '',
        status: ''
      },
      edificioForm: {
        nome: '',
        endereco: '',
        cidade: '',
        estado: '',
        cep: '',
        andares: '',
        descricao: '',
        esta_ativo: true
      },
      editingEdificio: null,
      stats: {
        total: 0,
        totalSalas: 0,
        capacidadeTotal: 0,
        localizacoes: 0
      },

      async init() {
        await this.loadData();
      },

      async loadData() {
        try {
          const response = await fetch('/api/edificios');
          if (response.ok) {
            this.edificios = await response.json();
            this.calculateStats();
          }
        } catch (error) {
          console.error('Erro ao carregar edifícios:', error);
          // Dados simulados para demonstração
          this.edificios = [
            {
              id: 1,
              nome: 'Edifício Principal',
              endereco: 'Rua das Empresas, 123',
              cidade: 'São Paulo',
              estado: 'SP',
              cep: '01234-567',
              andares: 10,
              descricao: 'Edifício principal da empresa',
              esta_ativo: true,
              total_salas: 25,
              capacidade_total: 500
            },
            {
              id: 2,
              nome: 'Edifício Anexo',
              endereco: 'Rua das Empresas, 125',
              cidade: 'São Paulo',
              estado: 'SP',
              cep: '01234-567',
              andares: 5,
              descricao: 'Edifício anexo para treinamentos',
              esta_ativo: true,
              total_salas: 12,
              capacidade_total: 240
            }
          ];
          this.calculateStats();
        }
      },

      calculateStats() {
        this.stats.total = this.edificios.length;
        this.stats.totalSalas = this.edificios.reduce((sum, e) => sum + (e.total_salas || 0), 0);
        this.stats.capacidadeTotal = this.edificios.reduce((sum, e) => sum + (e.capacidade_total || 0), 0);

        const cidades = new Set(this.edificios.map(e => e.cidade));
        this.stats.localizacoes = cidades.size;
      },

      get uniqueCidades() {
        return [...new Set(this.edificios.map(e => e.cidade))];
      },

      get filteredEdificios() {
        return this.edificios.filter(edificio => {
          if (this.filters.search) {
            const search = this.filters.search.toLowerCase();
            if (!edificio.nome.toLowerCase().includes(search) &&
                !edificio.endereco.toLowerCase().includes(search)) {
              return false;
            }
          }
          if (this.filters.cidade && edificio.cidade !== this.filters.cidade) {
            return false;
          }
          if (this.filters.status && edificio.esta_ativo.toString() !== this.filters.status) {
            return false;
          }
          return true;
        });
      },

      async createEdificio() {
        this.loading = true;
        try {
          const response = await fetch('/api/edificios', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json'
            },
            body: JSON.stringify(this.edificioForm)
          });

          const result = await response.json();

          if (response.ok) {
            alert('Edifício criado com sucesso!');
            this.closeModal();
            await this.loadData();
          } else {
            alert(result.message || 'Erro ao criar edifício');
          }
        } catch (error) {
          console.error('Erro ao criar edifício:', error);
          alert('Erro de conexão');
        } finally {
          this.loading = false;
        }
      },

      editEdificio(edificio) {
        this.editingEdificio = edificio;
        this.edificioForm = { ...edificio };
        this.showEditModal = true;
      },

      async updateEdificio() {
        this.loading = true;
        try {
          const response = await fetch(`/api/edificios/${this.editingEdificio.id}`, {
            method: 'PUT',
            headers: {
              'Content-Type': 'application/json'
            },
            body: JSON.stringify(this.edificioForm)
          });

          const result = await response.json();

          if (response.ok) {
            alert('Edifício atualizado com sucesso!');
            this.closeModal();
            await this.loadData();
          } else {
            alert(result.message || 'Erro ao atualizar edifício');
          }
        } catch (error) {
          console.error('Erro ao atualizar edifício:', error);
          alert('Erro de conexão');
        } finally {
          this.loading = false;
        }
      },

      async toggleStatus(edificio) {
        try {
          const response = await fetch(`/api/edificios/${edificio.id}`, {
            method: 'PUT',
            headers: {
              'Content-Type': 'application/json'
            },
            body: JSON.stringify({
              ...edificio,
              esta_ativo: !edificio.esta_ativo
            })
          });

          if (response.ok) {
            edificio.esta_ativo = !edificio.esta_ativo;
            alert(`Edifício ${edificio.esta_ativo ? 'ativado' : 'desativado'} com sucesso!`);
          } else {
            alert('Erro ao alterar status do edifício');
          }
        } catch (error) {
          console.error('Erro ao alterar status:', error);
          alert('Erro de conexão');
        }
      },

      viewSalas(edificio) {
        window.location.href = `/admin/salas?edificio=${edificio.id}`;
      },

      closeModal() {
        this.showCreateModal = false;
        this.showEditModal = false;
        this.editingEdificio = null;
        this.edificioForm = {
          nome: '',
          endereco: '',
          cidade: '',
          estado: '',
          cep: '',
          andares: '',
          descricao: '',
          esta_ativo: true
        };
      }
    }
  }
</script>
